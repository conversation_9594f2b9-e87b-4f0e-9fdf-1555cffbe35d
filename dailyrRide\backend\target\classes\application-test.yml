spring:
  datasource:
    url: jdbc:h2:mem:testdb
    username: sa
    password: 
    driver-class-name: org.h2.Driver
  
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.H2Dialect
        format_sql: true
  
  h2:
    console:
      enabled: true

# Test JWT settings
jwt:
  secret: test_jwt_secret_key
  expiration: 3600000 # 1 hour for tests

# Test logging
logging:
  level:
    com.dailyride: DEBUG
    org.springframework.test: DEBUG
    org.hibernate.SQL: DEBUG

# Test file upload
file:
  upload:
    path: "test-uploads/"
