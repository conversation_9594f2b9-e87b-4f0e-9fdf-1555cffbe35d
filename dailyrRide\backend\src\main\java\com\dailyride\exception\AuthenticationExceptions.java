package com.dailyride.exception;

/**
 * Authentication and authorization related exception classes.
 */
public class AuthenticationExceptions {
    
    public static class InvalidCredentialsException extends DailyRideException {
        public InvalidCredentialsException() {
            super("Invalid email/phone or password", "INVALID_CREDENTIALS");
        }
    }
    
    public static class UserAlreadyExistsException extends DailyRideException {
        public UserAlreadyExistsException(String identifier) {
            super("User already exists with: " + identifier, "USER_ALREADY_EXISTS");
        }
    }
    
    public static class AccountNotVerifiedException extends DailyRideException {
        public AccountNotVerifiedException() {
            super("Account is not verified. Please verify your email/phone", "ACCOUNT_NOT_VERIFIED");
        }
    }
    
    public static class AccountDeactivatedException extends DailyRideException {
        public AccountDeactivatedException() {
            super("Account has been deactivated", "ACCOUNT_DEACTIVATED");
        }
    }
    
    public static class InvalidTokenException extends DailyRideException {
        public InvalidTokenException(String message) {
            super("Invalid token: " + message, "INVALID_TOKEN");
        }
    }
    
    public static class TokenExpiredException extends DailyRideException {
        public TokenExpiredException() {
            super("Token has expired", "TOKEN_EXPIRED");
        }
    }
    
    public static class UnauthorizedAccessException extends DailyRideException {
        public UnauthorizedAccessException() {
            super("You are not authorized to perform this action", "UNAUTHORIZED_ACCESS");
        }
    }
    
    public static class WeakPasswordException extends DailyRideException {
        public WeakPasswordException(String requirements) {
            super("Password does not meet requirements: " + requirements, "WEAK_PASSWORD");
        }
    }
}
