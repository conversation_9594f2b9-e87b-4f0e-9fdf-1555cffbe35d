{"version": 3, "names": ["_core", "require", "buildForAwait", "template", "_default", "path", "getAsyncIterator", "node", "scope", "parent", "<PERSON><PERSON><PERSON>", "generateUidIdentifier", "<PERSON><PERSON><PERSON><PERSON>", "t", "memberExpression", "identifier", "left", "declar", "isIdentifier", "isPattern", "isMemberExpression", "expressionStatement", "assignmentExpression", "isVariableDeclaration", "variableDeclaration", "kind", "variableDeclarator", "declarations", "id", "ITERATOR_HAD_ERROR_KEY", "ITERATOR_ABRUPT_COMPLETION", "ITERATOR_ERROR_KEY", "ITERATOR_KEY", "GET_ITERATOR", "OBJECT", "right", "STEP_KEY", "cloneNode", "body", "isLabeledParent", "isLabeledStatement", "tryBody", "block", "loop", "labeledStatement", "label", "replaceParent"], "sources": ["../src/for-await.ts"], "sourcesContent": ["import { types as t, template, type NodePath } from \"@babel/core\";\n\nconst buildForAwait = template(`\n  async function wrapper() {\n    var ITERATOR_ABRUPT_COMPLETION = false;\n    var ITERATOR_HAD_ERROR_KEY = false;\n    var ITERATOR_ERROR_KEY;\n    try {\n      for (\n        var ITERATOR_KEY = GET_ITERATOR(OBJECT), STEP_KEY;\n        ITERATOR_ABRUPT_COMPLETION = !(STEP_KEY = await ITERATOR_KEY.next()).done;\n        ITERATOR_ABRUPT_COMPLETION = false\n      ) {\n      }\n    } catch (err) {\n      ITERATOR_HAD_ERROR_KEY = true;\n      ITERATOR_ERROR_KEY = err;\n    } finally {\n      try {\n        if (ITERATOR_ABRUPT_COMPLETION && ITERATOR_KEY.return != null) {\n          await ITERATOR_KEY.return();\n        }\n      } finally {\n        if (ITERATOR_HAD_ERROR_KEY) {\n          throw ITERATOR_ERROR_KEY;\n        }\n      }\n    }\n  }\n`);\n\nexport default function (\n  path: NodePath<t.ForOfStatement>,\n  { getAsyncIterator }: { getAsyncIterator: t.Identifier },\n) {\n  const { node, scope, parent } = path;\n\n  const stepKey = scope.generateUidIdentifier(\"step\");\n  const stepValue = t.memberExpression(stepKey, t.identifier(\"value\"));\n  const left = node.left;\n  let declar;\n\n  if (t.isIdentifier(left) || t.isPattern(left) || t.isMemberExpression(left)) {\n    // for await (i of test), for await ({ i } of test)\n    declar = t.expressionStatement(\n      t.assignmentExpression(\"=\", left, stepValue),\n    );\n  } else if (t.isVariableDeclaration(left)) {\n    // for await (let i of test)\n    declar = t.variableDeclaration(left.kind, [\n      t.variableDeclarator(left.declarations[0].id, stepValue),\n    ]);\n  }\n  let template = buildForAwait({\n    ITERATOR_HAD_ERROR_KEY: scope.generateUidIdentifier(\"didIteratorError\"),\n    ITERATOR_ABRUPT_COMPLETION: scope.generateUidIdentifier(\n      \"iteratorAbruptCompletion\",\n    ),\n    ITERATOR_ERROR_KEY: scope.generateUidIdentifier(\"iteratorError\"),\n    ITERATOR_KEY: scope.generateUidIdentifier(\"iterator\"),\n    GET_ITERATOR: getAsyncIterator,\n    OBJECT: node.right,\n    STEP_KEY: t.cloneNode(stepKey),\n  });\n\n  // remove async function wrapper\n  // @ts-expect-error todo(flow->ts) improve type annotation for buildForAwait\n  template = template.body.body as t.Statement[];\n\n  const isLabeledParent = t.isLabeledStatement(parent);\n  const tryBody = (template[3] as t.TryStatement).block.body;\n  const loop = tryBody[0] as t.ForStatement;\n\n  if (isLabeledParent) {\n    tryBody[0] = t.labeledStatement(parent.label, loop);\n  }\n\n  return {\n    replaceParent: isLabeledParent,\n    node: template,\n    declar,\n    loop,\n  };\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,KAAA,GAAAC,OAAA;AAEA,MAAMC,aAAa,GAAG,IAAAC,cAAQ,EAAC;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAAC;AAEa,SAAAC,SACbC,IAAgC,EAChC;EAAEC;AAAqD,CAAC,EACxD;EACA,MAAM;IAAEC,IAAI;IAAEC,KAAK;IAAEC;EAAO,CAAC,GAAGJ,IAAI;EAEpC,MAAMK,OAAO,GAAGF,KAAK,CAACG,qBAAqB,CAAC,MAAM,CAAC;EACnD,MAAMC,SAAS,GAAGC,WAAC,CAACC,gBAAgB,CAACJ,OAAO,EAAEG,WAAC,CAACE,UAAU,CAAC,OAAO,CAAC,CAAC;EACpE,MAAMC,IAAI,GAAGT,IAAI,CAACS,IAAI;EACtB,IAAIC,MAAM;EAEV,IAAIJ,WAAC,CAACK,YAAY,CAACF,IAAI,CAAC,IAAIH,WAAC,CAACM,SAAS,CAACH,IAAI,CAAC,IAAIH,WAAC,CAACO,kBAAkB,CAACJ,IAAI,CAAC,EAAE;IAE3EC,MAAM,GAAGJ,WAAC,CAACQ,mBAAmB,CAC5BR,WAAC,CAACS,oBAAoB,CAAC,GAAG,EAAEN,IAAI,EAAEJ,SAAS,CAC7C,CAAC;EACH,CAAC,MAAM,IAAIC,WAAC,CAACU,qBAAqB,CAACP,IAAI,CAAC,EAAE;IAExCC,MAAM,GAAGJ,WAAC,CAACW,mBAAmB,CAACR,IAAI,CAACS,IAAI,EAAE,CACxCZ,WAAC,CAACa,kBAAkB,CAACV,IAAI,CAACW,YAAY,CAAC,CAAC,CAAC,CAACC,EAAE,EAAEhB,SAAS,CAAC,CACzD,CAAC;EACJ;EACA,IAAIT,QAAQ,GAAGD,aAAa,CAAC;IAC3B2B,sBAAsB,EAAErB,KAAK,CAACG,qBAAqB,CAAC,kBAAkB,CAAC;IACvEmB,0BAA0B,EAAEtB,KAAK,CAACG,qBAAqB,CACrD,0BACF,CAAC;IACDoB,kBAAkB,EAAEvB,KAAK,CAACG,qBAAqB,CAAC,eAAe,CAAC;IAChEqB,YAAY,EAAExB,KAAK,CAACG,qBAAqB,CAAC,UAAU,CAAC;IACrDsB,YAAY,EAAE3B,gBAAgB;IAC9B4B,MAAM,EAAE3B,IAAI,CAAC4B,KAAK;IAClBC,QAAQ,EAAEvB,WAAC,CAACwB,SAAS,CAAC3B,OAAO;EAC/B,CAAC,CAAC;EAIFP,QAAQ,GAAGA,QAAQ,CAACmC,IAAI,CAACA,IAAqB;EAE9C,MAAMC,eAAe,GAAG1B,WAAC,CAAC2B,kBAAkB,CAAC/B,MAAM,CAAC;EACpD,MAAMgC,OAAO,GAAItC,QAAQ,CAAC,CAAC,CAAC,CAAoBuC,KAAK,CAACJ,IAAI;EAC1D,MAAMK,IAAI,GAAGF,OAAO,CAAC,CAAC,CAAmB;EAEzC,IAAIF,eAAe,EAAE;IACnBE,OAAO,CAAC,CAAC,CAAC,GAAG5B,WAAC,CAAC+B,gBAAgB,CAACnC,MAAM,CAACoC,KAAK,EAAEF,IAAI,CAAC;EACrD;EAEA,OAAO;IACLG,aAAa,EAAEP,eAAe;IAC9BhC,IAAI,EAAEJ,QAAQ;IACdc,MAAM;IACN0B;EACF,CAAC;AACH", "ignoreList": []}