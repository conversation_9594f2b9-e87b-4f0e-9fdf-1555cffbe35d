import * as SQLite from 'expo-sqlite';
import { databaseService } from '@/services/database';

/**
 * 🔄 Database Reset Utility
 * Clears and recreates the database with the correct schema
 */

const DATABASE_NAME = 'dailyride.db';

export const resetDatabase = async (): Promise<void> => {
  try {
    console.log('🔄 Starting database reset...');
    
    // Close existing database connection
    const db = await SQLite.openDatabaseAsync(DATABASE_NAME);
    
    // Drop all tables
    const tables = [
      'driver_verifications',
      'bids',
      'social_interactions',
      'badges',
      'ratings',
      'security_alerts',
      'emergency_contacts',
      'notifications',
      'ride_requests',
      'bookings',
      'trips',
      'users',
      'migrations'
    ];
    
    for (const table of tables) {
      try {
        await db.execAsync(`DROP TABLE IF EXISTS ${table}`);
        console.log(`🗑️ Dropped table: ${table}`);
      } catch (error) {
        console.log(`⚠️ Could not drop table ${table}:`, error);
      }
    }
    
    console.log('✅ All tables dropped successfully');
    
    // Reinitialize database service
    await databaseService.init();
    
    console.log('🎉 Database reset completed successfully!');
    console.log('📊 Database is now ready with the correct schema');
    
  } catch (error) {
    console.error('❌ Database reset failed:', error);
    throw error;
  }
};

export const clearDatabaseData = async (): Promise<void> => {
  try {
    console.log('🧹 Clearing database data...');
    
    const db = await SQLite.openDatabaseAsync(DATABASE_NAME);
    
    // Clear data from all tables (but keep structure)
    const tables = [
      'driver_verifications',
      'bids',
      'social_interactions',
      'badges',
      'ratings',
      'security_alerts',
      'emergency_contacts',
      'notifications',
      'ride_requests',
      'bookings',
      'trips',
      'users'
    ];
    
    for (const table of tables) {
      try {
        await db.execAsync(`DELETE FROM ${table}`);
        console.log(`🧹 Cleared data from: ${table}`);
      } catch (error) {
        console.log(`⚠️ Could not clear table ${table}:`, error);
      }
    }
    
    console.log('✅ Database data cleared successfully');
    
  } catch (error) {
    console.error('❌ Database data clearing failed:', error);
    throw error;
  }
};
