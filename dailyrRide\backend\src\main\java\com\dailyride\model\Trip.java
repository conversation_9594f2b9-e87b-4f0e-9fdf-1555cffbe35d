package com.dailyride.model;

import jakarta.persistence.*;
import jakarta.validation.constraints.*;

/**
 * Trip entity representing ride offers in the DailyRide system.
 * Stored in SQLite 'trips' table.
 */
@Entity
@Table(name = "trips")
public class Trip {

    @Id
    private String id;

    @Column(name = "createdAt")
    private String createdAt;

    @Column(name = "updatedAt")
    private String updatedAt;

    @Column(nullable = false)
    private String driverId;

    @Column(nullable = false)
    private String originAddress;

    @Column(nullable = false)
    private Double originLatitude;

    @Column(nullable = false)
    private Double originLongitude;

    @Column(nullable = false)
    private String destinationAddress;

    @Column(nullable = false)
    private Double destinationLatitude;

    @Column(nullable = false)
    private Double destinationLongitude;

    @Column(nullable = false)
    private String departureDateTime;

    @Column(nullable = false)
    private Integer availableSeats;

    @Column(nullable = false)
    private Double pricePerSeat;

    @Column
    private String currency = "MAD";

    @Column
    private String description;

    @Column
    @Enumerated(EnumType.STRING)
    private TripStatus status = TripStatus.ACTIVE;

    @Column
    private String vehicleInfo;

    @Column
    private String preferences;

    // Enums
    public enum TripStatus {
        ACTIVE,
        COMPLETED,
        CANCELLED
    }

    // Constructors
    public Trip() {}

    public Trip(String driverId, String originAddress, Double originLatitude, Double originLongitude,
                String destinationAddress, Double destinationLatitude, Double destinationLongitude,
                String departureDateTime, Integer availableSeats, Double pricePerSeat) {
        this.id = "trip-" + System.currentTimeMillis() + "-" + Math.random();
        this.driverId = driverId;
        this.originAddress = originAddress;
        this.originLatitude = originLatitude;
        this.originLongitude = originLongitude;
        this.destinationAddress = destinationAddress;
        this.destinationLatitude = destinationLatitude;
        this.destinationLongitude = destinationLongitude;
        this.departureDateTime = departureDateTime;
        this.availableSeats = availableSeats;
        this.pricePerSeat = pricePerSeat;
        this.createdAt = java.time.LocalDateTime.now().toString();
        this.updatedAt = java.time.LocalDateTime.now().toString();
    }

    // Business methods
    public boolean isActive() {
        return status == TripStatus.ACTIVE;
    }

    public boolean hasAvailableSeats() {
        return availableSeats != null && availableSeats > 0;
    }

    public void bookSeats(int seatsToBook) {
        if (availableSeats != null && availableSeats >= seatsToBook) {
            this.availableSeats -= seatsToBook;
            this.updatedAt = java.time.LocalDateTime.now().toString();
        }
    }

    public void cancelBooking(int seatsToCancel) {
        if (availableSeats != null) {
            this.availableSeats += seatsToCancel;
            this.updatedAt = java.time.LocalDateTime.now().toString();
        }
    }

    // Getters and Setters
    public String getId() { return id; }
    public void setId(String id) { this.id = id; }

    public String getCreatedAt() { return createdAt; }
    public void setCreatedAt(String createdAt) { this.createdAt = createdAt; }

    public String getUpdatedAt() { return updatedAt; }
    public void setUpdatedAt(String updatedAt) { this.updatedAt = updatedAt; }

    public String getDriverId() { return driverId; }
    public void setDriverId(String driverId) { this.driverId = driverId; }

    public String getOriginAddress() { return originAddress; }
    public void setOriginAddress(String originAddress) { this.originAddress = originAddress; }

    public Double getOriginLatitude() { return originLatitude; }
    public void setOriginLatitude(Double originLatitude) { this.originLatitude = originLatitude; }

    public Double getOriginLongitude() { return originLongitude; }
    public void setOriginLongitude(Double originLongitude) { this.originLongitude = originLongitude; }

    public String getDestinationAddress() { return destinationAddress; }
    public void setDestinationAddress(String destinationAddress) { this.destinationAddress = destinationAddress; }

    public Double getDestinationLatitude() { return destinationLatitude; }
    public void setDestinationLatitude(Double destinationLatitude) { this.destinationLatitude = destinationLatitude; }

    public Double getDestinationLongitude() { return destinationLongitude; }
    public void setDestinationLongitude(Double destinationLongitude) { this.destinationLongitude = destinationLongitude; }

    public String getDepartureDateTime() { return departureDateTime; }
    public void setDepartureDateTime(String departureDateTime) { this.departureDateTime = departureDateTime; }

    public Integer getAvailableSeats() { return availableSeats; }
    public void setAvailableSeats(Integer availableSeats) { this.availableSeats = availableSeats; }

    public Double getPricePerSeat() { return pricePerSeat; }
    public void setPricePerSeat(Double pricePerSeat) { this.pricePerSeat = pricePerSeat; }

    public String getCurrency() { return currency; }
    public void setCurrency(String currency) { this.currency = currency; }

    public String getDescription() { return description; }
    public void setDescription(String description) { this.description = description; }

    public TripStatus getStatus() { return status; }
    public void setStatus(TripStatus status) { 
        this.status = status; 
        this.updatedAt = java.time.LocalDateTime.now().toString();
    }

    public String getVehicleInfo() { return vehicleInfo; }
    public void setVehicleInfo(String vehicleInfo) { this.vehicleInfo = vehicleInfo; }

    public String getPreferences() { return preferences; }
    public void setPreferences(String preferences) { this.preferences = preferences; }

    // Compatibility methods for existing services
    public String getOrigin() { return originAddress; }
    public void setOrigin(String origin) { this.originAddress = origin; }

    public String getDestination() { return destinationAddress; }
    public void setDestination(String destination) { this.destinationAddress = destination; }

    public Double getPrice() { return pricePerSeat; }
    public void setPrice(Double price) { this.pricePerSeat = price; }

    public String getNotes() { return description; }
    public void setNotes(String notes) { this.description = notes; }

    @Override
    public String toString() {
        return "Trip{" +
                "id='" + id + '\'' +
                ", driverId='" + driverId + '\'' +
                ", originAddress='" + originAddress + '\'' +
                ", destinationAddress='" + destinationAddress + '\'' +
                ", departureDateTime='" + departureDateTime + '\'' +
                ", availableSeats=" + availableSeats +
                ", pricePerSeat=" + pricePerSeat +
                ", status=" + status +
                '}';
    }
}
