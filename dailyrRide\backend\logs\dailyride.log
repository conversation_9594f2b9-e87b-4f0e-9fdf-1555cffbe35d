2025-06-05 01:05:56 [main] INFO  com.dailyride.DailyRideApplication - Starting DailyRideApplication using Java 21.0.7 with PID 3972 (C:\Users\<USER>\Downloads\dailyrRide\dailyrRide\backend\target\classes started by medad in C:\Users\<USER>\Downloads\dailyrRide\dailyrRide\backend)
2025-06-05 01:05:56 [main] DEBUG com.dailyride.DailyRideApplication - Running with Spring Boot v3.2.0, Spring v6.1.1
2025-06-05 01:05:56 [main] INFO  com.dailyride.DailyRideApplication - The following 1 profile is active: "development"
2025-06-05 01:06:00 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-06-05 01:06:00 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-05 01:06:00 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.16]
2025-06-05 01:06:00 [main] INFO  o.a.c.c.C.[.[localhost].[/api] - Initializing Spring embedded WebApplicationContext
2025-06-05 01:06:00 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3868 ms
2025-06-05 01:06:01 [main] DEBUG o.s.w.f.ServerHttpObservationFilter - Filter 'webMvcObservationFilter' configured for use
2025-06-05 01:06:03 [main] INFO  c.d.service.GoogleMapsService - 🗺️ Google Maps service initialized successfully
2025-06-05 01:06:04 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - 31 mappings in 'requestMappingHandlerMapping'
2025-06-05 01:06:04 [main] DEBUG o.s.w.s.h.SimpleUrlHandlerMapping - Patterns [/webjars/**, /**, /swagger-ui*/*swagger-initializer.js, /swagger-ui*/**] in 'resourceHandlerMapping'
2025-06-05 01:06:04 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerAdapter - ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
2025-06-05 01:06:04 [main] DEBUG o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - ControllerAdvice beans: 1 @ExceptionHandler, 1 ResponseBodyAdvice
2025-06-05 01:06:05 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 2b6ceac2-de1a-4158-b06e-d80e4788dc52

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-06-05 01:06:05 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 3 endpoint(s) beneath base path '/actuator'
2025-06-05 01:06:05 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@571c2ed8, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@5810772a, org.springframework.security.web.context.SecurityContextHolderFilter@6fad5ace, org.springframework.security.web.header.HeaderWriterFilter@3f5ac587, org.springframework.web.filter.CorsFilter@4eacb6d1, org.springframework.security.web.csrf.CsrfFilter@115a7e51, org.springframework.security.web.authentication.logout.LogoutFilter@73b3ce31, org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter@10ec4721, org.springframework.security.web.authentication.ui.DefaultLoginPageGeneratingFilter@63ed5dae, org.springframework.security.web.authentication.ui.DefaultLogoutPageGeneratingFilter@3ccefe1b, org.springframework.security.web.authentication.www.BasicAuthenticationFilter@25c548d1, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@35f6f105, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@3aa1c45, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@a926db4, org.springframework.security.web.access.ExceptionTranslationFilter@34f24a11, org.springframework.security.web.access.intercept.AuthorizationFilter@5feff876]
2025-06-05 01:06:05 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'
2025-06-05 01:06:05 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-06-05 01:06:05 [main] ERROR o.s.b.d.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 8080 was already in use.

Action:

Identify and stop the process that's listening on port 8080 or configure this application to listen on another port.

2025-06-05 01:07:57 [main] INFO  com.dailyride.DailyRideApplication - Starting DailyRideApplication using Java 21.0.7 with PID 2460 (C:\Users\<USER>\Downloads\dailyrRide\dailyrRide\backend\target\classes started by medad in C:\Users\<USER>\Downloads\dailyrRide\dailyrRide\backend)
2025-06-05 01:07:57 [main] DEBUG com.dailyride.DailyRideApplication - Running with Spring Boot v3.2.0, Spring v6.1.1
2025-06-05 01:07:57 [main] INFO  com.dailyride.DailyRideApplication - The following 1 profile is active: "development"
2025-06-05 01:07:59 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8090 (http)
2025-06-05 01:07:59 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-05 01:07:59 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.16]
2025-06-05 01:08:00 [main] INFO  o.a.c.c.C.[.[localhost].[/api] - Initializing Spring embedded WebApplicationContext
2025-06-05 01:08:00 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2080 ms
2025-06-05 01:08:00 [main] DEBUG o.s.w.f.ServerHttpObservationFilter - Filter 'webMvcObservationFilter' configured for use
2025-06-05 01:08:01 [main] INFO  c.d.service.GoogleMapsService - 🗺️ Google Maps service initialized successfully
2025-06-05 01:08:02 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - 31 mappings in 'requestMappingHandlerMapping'
2025-06-05 01:08:02 [main] DEBUG o.s.w.s.h.SimpleUrlHandlerMapping - Patterns [/webjars/**, /**, /swagger-ui*/*swagger-initializer.js, /swagger-ui*/**] in 'resourceHandlerMapping'
2025-06-05 01:08:02 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerAdapter - ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
2025-06-05 01:08:02 [main] DEBUG o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - ControllerAdvice beans: 1 @ExceptionHandler, 1 ResponseBodyAdvice
2025-06-05 01:08:03 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 60f945b6-a1d6-4110-8559-0b7a2c019237

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-06-05 01:08:03 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 3 endpoint(s) beneath base path '/actuator'
2025-06-05 01:08:03 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@52e92f6, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@17034458, org.springframework.security.web.context.SecurityContextHolderFilter@d1d85d0, org.springframework.security.web.header.HeaderWriterFilter@25172993, org.springframework.web.filter.CorsFilter@3e0e0ba7, org.springframework.security.web.csrf.CsrfFilter@24731caf, org.springframework.security.web.authentication.logout.LogoutFilter@3aa1c45, org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter@34b462e0, org.springframework.security.web.authentication.ui.DefaultLoginPageGeneratingFilter@4e0802e0, org.springframework.security.web.authentication.ui.DefaultLogoutPageGeneratingFilter@7df5549e, org.springframework.security.web.authentication.www.BasicAuthenticationFilter@117069f2, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@23ea3853, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@6f1163f7, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@cbdc0f4, org.springframework.security.web.access.ExceptionTranslationFilter@4cd51e4e, org.springframework.security.web.access.intercept.AuthorizationFilter@62f6185a]
2025-06-05 01:08:03 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8090 (http) with context path '/api'
2025-06-05 01:08:03 [main] INFO  com.dailyride.DailyRideApplication - Started DailyRideApplication in 6.404 seconds (process running for 7.261)
2025-06-05 01:10:02 [main] INFO  com.dailyride.DailyRideApplication - Starting DailyRideApplication using Java 21.0.7 with PID 22048 (C:\Users\<USER>\Downloads\dailyrRide\dailyrRide\backend\target\classes started by medad in C:\Users\<USER>\Downloads\dailyrRide\dailyrRide\backend)
2025-06-05 01:10:02 [main] DEBUG com.dailyride.DailyRideApplication - Running with Spring Boot v3.2.0, Spring v6.1.1
2025-06-05 01:10:02 [main] INFO  com.dailyride.DailyRideApplication - The following 1 profile is active: "development"
2025-06-05 01:10:04 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8090 (http)
2025-06-05 01:10:04 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-05 01:10:04 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.16]
2025-06-05 01:10:05 [main] INFO  o.a.c.c.C.[.[localhost].[/api] - Initializing Spring embedded WebApplicationContext
2025-06-05 01:10:05 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2726 ms
2025-06-05 01:10:05 [main] DEBUG o.s.w.f.ServerHttpObservationFilter - Filter 'webMvcObservationFilter' configured for use
2025-06-05 01:10:06 [main] INFO  c.d.service.GoogleMapsService - 🗺️ Google Maps service initialized successfully
2025-06-05 01:10:07 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - 31 mappings in 'requestMappingHandlerMapping'
2025-06-05 01:10:07 [main] DEBUG o.s.w.s.h.SimpleUrlHandlerMapping - Patterns [/webjars/**, /**, /swagger-ui*/*swagger-initializer.js, /swagger-ui*/**] in 'resourceHandlerMapping'
2025-06-05 01:10:07 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerAdapter - ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
2025-06-05 01:10:07 [main] DEBUG o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - ControllerAdvice beans: 1 @ExceptionHandler, 1 ResponseBodyAdvice
2025-06-05 01:10:08 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 6ca98b3e-5e7f-47b8-a929-8bed4c24dca3

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-06-05 01:10:08 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 3 endpoint(s) beneath base path '/actuator'
2025-06-05 01:10:08 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@cbdc0f4, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@11174bf, org.springframework.security.web.context.SecurityContextHolderFilter@4ca907af, org.springframework.security.web.header.HeaderWriterFilter@23ea3853, org.springframework.web.filter.CorsFilter@4f0c1409, org.springframework.security.web.csrf.CsrfFilter@e27d97b, org.springframework.security.web.authentication.logout.LogoutFilter@63ed5dae, org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter@96dfcbb, org.springframework.security.web.authentication.ui.DefaultLoginPageGeneratingFilter@7b54a0a4, org.springframework.security.web.authentication.ui.DefaultLogoutPageGeneratingFilter@188ae8d2, org.springframework.security.web.authentication.www.BasicAuthenticationFilter@17034458, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@5a99543b, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@4e0802e0, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@7a522157, org.springframework.security.web.access.ExceptionTranslationFilter@d1d85d0, org.springframework.security.web.access.intercept.AuthorizationFilter@1e592ef2]
2025-06-05 01:10:08 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8090 (http) with context path '/api'
2025-06-05 01:10:08 [main] INFO  com.dailyride.DailyRideApplication - Started DailyRideApplication in 7.211 seconds (process running for 7.981)
2025-06-05 01:11:07 [http-nio-8090-exec-2] INFO  o.a.c.c.C.[.[localhost].[/api] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-05 01:11:07 [http-nio-8090-exec-2] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-05 01:11:07 [http-nio-8090-exec-2] DEBUG o.s.web.servlet.DispatcherServlet - Detected StandardServletMultipartResolver
2025-06-05 01:11:07 [http-nio-8090-exec-2] DEBUG o.s.web.servlet.DispatcherServlet - Detected AcceptHeaderLocaleResolver
2025-06-05 01:11:07 [http-nio-8090-exec-2] DEBUG o.s.web.servlet.DispatcherServlet - Detected FixedThemeResolver
2025-06-05 01:11:07 [http-nio-8090-exec-2] DEBUG o.s.web.servlet.DispatcherServlet - Detected org.springframework.web.servlet.view.DefaultRequestToViewNameTranslator@136a032f
2025-06-05 01:11:07 [http-nio-8090-exec-2] DEBUG o.s.web.servlet.DispatcherServlet - Detected org.springframework.web.servlet.support.SessionFlashMapManager@66727a09
2025-06-05 01:11:07 [http-nio-8090-exec-2] DEBUG o.s.web.servlet.DispatcherServlet - enableLoggingRequestDetails='false': request parameters and headers will be masked to prevent unsafe logging of potentially sensitive data
2025-06-05 01:11:07 [http-nio-8090-exec-2] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 58 ms
2025-06-05 01:11:07 [http-nio-8090-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing GET /actuator/health
2025-06-05 01:11:07 [http-nio-8090-exec-2] WARN  o.s.w.s.h.HandlerMappingIntrospector - Cache miss for REQUEST dispatch to '/api/actuator/health' (previous null). Performing CorsConfiguration lookup. This is logged once only at WARN level, and every time at TRACE.
2025-06-05 01:11:08 [http-nio-8090-exec-2] DEBUG o.s.security.web.FilterChainProxy - Secured GET /actuator/health
2025-06-05 01:11:08 [http-nio-8090-exec-2] DEBUG o.s.web.servlet.DispatcherServlet - GET "/api/actuator/health", parameters={}
2025-06-05 01:11:08 [http-nio-8090-exec-2] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Read "application/octet-stream" to []
2025-06-05 01:11:08 [http-nio-8090-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-05 01:11:08 [http-nio-8090-exec-2] DEBUG o.s.w.s.m.m.a.HttpEntityMethodProcessor - Using 'application/vnd.spring-boot.actuator.v3+json;q=0.8', given [text/html, application/xhtml+xml, image/avif, image/webp, image/apng, application/xml;q=0.9, */*;q=0.8, application/signed-exchange;v=b3;q=0.7] and supported [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/json]
2025-06-05 01:11:08 [http-nio-8090-exec-2] DEBUG o.s.w.s.m.m.a.HttpEntityMethodProcessor - Writing [org.springframework.boot.actuate.health.SystemHealth@225e399c]
2025-06-05 01:11:08 [http-nio-8090-exec-2] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-06-05 01:11:13 [http-nio-8090-exec-5] DEBUG o.s.security.web.FilterChainProxy - Securing GET /swagger-ui.html
2025-06-05 01:11:13 [http-nio-8090-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-05 01:11:14 [http-nio-8090-exec-5] DEBUG o.s.s.w.s.HttpSessionRequestCache - Saved request http://localhost:8090/api/swagger-ui.html?continue to session
2025-06-05 01:11:14 [http-nio-8090-exec-5] DEBUG o.s.s.w.a.DelegatingAuthenticationEntryPoint - Trying to match using And [Not [RequestHeaderRequestMatcher [expectedHeaderName=X-Requested-With, expectedHeaderValue=XMLHttpRequest]], MediaTypeRequestMatcher [contentNegotiationStrategy=org.springframework.web.accept.ContentNegotiationManager@5b356741, matchingMediaTypes=[application/xhtml+xml, image/*, text/html, text/plain], useEquals=false, ignoredMediaTypes=[*/*]]]
2025-06-05 01:11:14 [http-nio-8090-exec-5] DEBUG o.s.s.w.a.DelegatingAuthenticationEntryPoint - Match found! Executing org.springframework.security.web.authentication.LoginUrlAuthenticationEntryPoint@225ac5e7
2025-06-05 01:11:14 [http-nio-8090-exec-5] DEBUG o.s.s.web.DefaultRedirectStrategy - Redirecting to http://localhost:8090/api/login
2025-06-05 01:11:14 [http-nio-8090-exec-6] DEBUG o.s.security.web.FilterChainProxy - Securing GET /login
2025-06-05 01:16:07 [http-nio-8090-exec-8] DEBUG o.s.security.web.FilterChainProxy - Securing GET /actuator/health
2025-06-05 01:16:07 [http-nio-8090-exec-8] DEBUG o.s.security.web.FilterChainProxy - Secured GET /actuator/health
2025-06-05 01:16:07 [http-nio-8090-exec-8] DEBUG o.s.web.servlet.DispatcherServlet - GET "/api/actuator/health", parameters={}
2025-06-05 01:16:07 [http-nio-8090-exec-8] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Read "application/octet-stream" to []
2025-06-05 01:16:07 [http-nio-8090-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-05 01:16:07 [http-nio-8090-exec-8] DEBUG o.s.w.s.m.m.a.HttpEntityMethodProcessor - Using 'application/vnd.spring-boot.actuator.v3+json', given [*/*] and supported [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/json]
2025-06-05 01:16:07 [http-nio-8090-exec-8] DEBUG o.s.w.s.m.m.a.HttpEntityMethodProcessor - Writing [org.springframework.boot.actuate.health.SystemHealth@4adf07a]
2025-06-05 01:16:07 [http-nio-8090-exec-8] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-06-05 01:21:39 [main] INFO  com.dailyride.DailyRideApplication - Starting DailyRideApplication using Java 21.0.7 with PID 5592 (C:\Users\<USER>\Downloads\dailyrRide\dailyrRide\backend\target\classes started by medad in C:\Users\<USER>\Downloads\dailyrRide\dailyrRide\backend)
2025-06-05 01:21:39 [main] DEBUG com.dailyride.DailyRideApplication - Running with Spring Boot v3.2.0, Spring v6.1.1
2025-06-05 01:21:39 [main] INFO  com.dailyride.DailyRideApplication - The following 1 profile is active: "development"
2025-06-05 01:21:41 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8090 (http)
2025-06-05 01:21:41 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-05 01:21:41 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.16]
2025-06-05 01:21:42 [main] INFO  o.a.c.c.C.[.[localhost].[/api] - Initializing Spring embedded WebApplicationContext
2025-06-05 01:21:42 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2128 ms
2025-06-05 01:21:42 [main] DEBUG o.s.w.f.ServerHttpObservationFilter - Filter 'webMvcObservationFilter' configured for use
2025-06-05 01:21:43 [main] INFO  c.d.service.GoogleMapsService - 🗺️ Google Maps service initialized successfully
2025-06-05 01:21:44 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - 31 mappings in 'requestMappingHandlerMapping'
2025-06-05 01:21:44 [main] DEBUG o.s.w.s.h.SimpleUrlHandlerMapping - Patterns [/webjars/**, /**, /swagger-ui*/*swagger-initializer.js, /swagger-ui*/**] in 'resourceHandlerMapping'
2025-06-05 01:21:44 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerAdapter - ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
2025-06-05 01:21:44 [main] DEBUG o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - ControllerAdvice beans: 1 @ExceptionHandler, 1 ResponseBodyAdvice
2025-06-05 01:21:44 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: b8f0463e-1c5d-440b-af76-2875d0ed25ff

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-06-05 01:21:44 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 3 endpoint(s) beneath base path '/actuator'
2025-06-05 01:21:44 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@7df5549e, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@cbdc0f4, org.springframework.security.web.context.SecurityContextHolderFilter@23ea3853, org.springframework.security.web.header.HeaderWriterFilter@5b32e0b1, org.springframework.web.filter.CorsFilter@11174bf, org.springframework.security.web.csrf.CsrfFilter@486dd616, org.springframework.security.web.authentication.logout.LogoutFilter@1614499b, org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter@1e592ef2, org.springframework.security.web.authentication.ui.DefaultLoginPageGeneratingFilter@687f62a5, org.springframework.security.web.authentication.ui.DefaultLogoutPageGeneratingFilter@4f0c1409, org.springframework.security.web.authentication.www.BasicAuthenticationFilter@52e92f6, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@e4348c0, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@4ca907af, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@188ae8d2, org.springframework.security.web.access.ExceptionTranslationFilter@25172993, org.springframework.security.web.access.intercept.AuthorizationFilter@4c3fcbe7]
2025-06-05 01:21:44 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8090 (http) with context path '/api'
2025-06-05 01:21:44 [main] INFO  com.dailyride.DailyRideApplication - Started DailyRideApplication in 5.913 seconds (process running for 6.658)
2025-06-05 01:52:44 [main] INFO  com.dailyride.DailyRideApplication - Starting DailyRideApplication using Java 21.0.7 with PID 9724 (C:\Users\<USER>\Downloads\dailyrRide\dailyrRide\backend\target\classes started by medad in C:\Users\<USER>\Downloads\dailyrRide\dailyrRide\backend)
2025-06-05 01:52:44 [main] DEBUG com.dailyride.DailyRideApplication - Running with Spring Boot v3.2.0, Spring v6.1.1
2025-06-05 01:52:44 [main] INFO  com.dailyride.DailyRideApplication - The following 1 profile is active: "development"
2025-06-05 01:52:47 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8090 (http)
2025-06-05 01:52:47 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-05 01:52:47 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.16]
2025-06-05 01:52:47 [main] INFO  o.a.c.c.C.[.[localhost].[/api] - Initializing Spring embedded WebApplicationContext
2025-06-05 01:52:47 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2559 ms
2025-06-05 01:52:47 [main] DEBUG o.s.w.f.ServerHttpObservationFilter - Filter 'webMvcObservationFilter' configured for use
2025-06-05 01:52:49 [main] INFO  c.d.service.GoogleMapsService - 🗺️ Google Maps service initialized successfully
2025-06-05 01:52:49 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - 31 mappings in 'requestMappingHandlerMapping'
2025-06-05 01:52:49 [main] DEBUG o.s.w.s.h.SimpleUrlHandlerMapping - Patterns [/webjars/**, /**, /swagger-ui*/*swagger-initializer.js, /swagger-ui*/**] in 'resourceHandlerMapping'
2025-06-05 01:52:50 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerAdapter - ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
2025-06-05 01:52:50 [main] DEBUG o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - ControllerAdvice beans: 1 @ExceptionHandler, 1 ResponseBodyAdvice
2025-06-05 01:52:50 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 54f3ad89-1417-4151-9dde-79b127412d91

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-06-05 01:52:50 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 3 endpoint(s) beneath base path '/actuator'
2025-06-05 01:52:50 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@188ae8d2, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@7a522157, org.springframework.security.web.context.SecurityContextHolderFilter@4a18e0f1, org.springframework.security.web.header.HeaderWriterFilter@3ef396c9, org.springframework.web.filter.CorsFilter@706c062e, org.springframework.security.web.csrf.CsrfFilter@73dd0f23, org.springframework.security.web.authentication.logout.LogoutFilter@566cc6af, org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter@51c6e775, org.springframework.security.web.authentication.ui.DefaultLoginPageGeneratingFilter@30a20fb3, org.springframework.security.web.authentication.ui.DefaultLogoutPageGeneratingFilter@5feff876, org.springframework.security.web.authentication.www.BasicAuthenticationFilter@cbdc0f4, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@7857cb1d, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@7646c9f5, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@10ec4721, org.springframework.security.web.access.ExceptionTranslationFilter@e4348c0, org.springframework.security.web.access.intercept.AuthorizationFilter@6bf77ee]
2025-06-05 01:52:50 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8090 (http) with context path '/api'
2025-06-05 01:52:50 [main] INFO  com.dailyride.DailyRideApplication - Started DailyRideApplication in 7.127 seconds (process running for 8.003)
2025-06-05 02:17:51 [main] INFO  com.dailyride.DailyRideApplication - Starting DailyRideApplication using Java 21.0.7 with PID 7048 (C:\Users\<USER>\Downloads\dailyrRide\dailyrRide\backend\target\classes started by medad in C:\Users\<USER>\Downloads\dailyrRide\dailyrRide\backend)
2025-06-05 02:17:51 [main] DEBUG com.dailyride.DailyRideApplication - Running with Spring Boot v3.2.0, Spring v6.1.1
2025-06-05 02:17:51 [main] INFO  com.dailyride.DailyRideApplication - The following 1 profile is active: "development"
2025-06-05 02:17:54 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8090 (http)
2025-06-05 02:17:54 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-05 02:17:54 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.16]
2025-06-05 02:17:54 [main] INFO  o.a.c.c.C.[.[localhost].[/api] - Initializing Spring embedded WebApplicationContext
2025-06-05 02:17:54 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3793 ms
2025-06-05 02:17:55 [main] DEBUG o.s.w.f.ServerHttpObservationFilter - Filter 'webMvcObservationFilter' configured for use
2025-06-05 02:17:57 [main] INFO  c.d.service.GoogleMapsService - 🗺️ Google Maps service initialized successfully
2025-06-05 02:17:57 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - 31 mappings in 'requestMappingHandlerMapping'
2025-06-05 02:17:58 [main] DEBUG o.s.w.s.h.SimpleUrlHandlerMapping - Patterns [/webjars/**, /**, /swagger-ui*/*swagger-initializer.js, /swagger-ui*/**] in 'resourceHandlerMapping'
2025-06-05 02:17:58 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerAdapter - ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
2025-06-05 02:17:58 [main] DEBUG o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - ControllerAdvice beans: 1 @ExceptionHandler, 1 ResponseBodyAdvice
2025-06-05 02:17:58 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: ce9963ee-dcf6-4b8b-a022-f1164ea00fdb

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-06-05 02:17:58 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 3 endpoint(s) beneath base path '/actuator'
2025-06-05 02:17:58 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@7a522157, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@706c062e, org.springframework.security.web.context.SecurityContextHolderFilter@687f62a5, org.springframework.security.web.header.HeaderWriterFilter@5a99543b, org.springframework.web.filter.CorsFilter@5feff876, org.springframework.security.web.csrf.CsrfFilter@38d4488c, org.springframework.security.web.authentication.logout.LogoutFilter@d70dbeb, org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter@372841d2, org.springframework.security.web.authentication.ui.DefaultLoginPageGeneratingFilter@34f24a11, org.springframework.security.web.authentication.ui.DefaultLogoutPageGeneratingFilter@10ec4721, org.springframework.security.web.authentication.www.BasicAuthenticationFilter@11174bf, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@7646c9f5, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@7b54a0a4, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@7bdf94f2, org.springframework.security.web.access.ExceptionTranslationFilter@4ca907af, org.springframework.security.web.access.intercept.AuthorizationFilter@51c6e775]
2025-06-05 02:17:59 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8090 (http) with context path '/api'
2025-06-05 02:17:59 [main] INFO  com.dailyride.DailyRideApplication - Started DailyRideApplication in 9.1 seconds (process running for 9.905)
2025-06-05 02:43:40 [main] INFO  com.dailyride.DailyRideApplication - Starting DailyRideApplication using Java 21.0.7 with PID 26032 (C:\Users\<USER>\Downloads\dailyrRide\dailyrRide\backend\target\classes started by medad in C:\Users\<USER>\Downloads\dailyrRide\dailyrRide\backend)
2025-06-05 02:43:40 [main] DEBUG com.dailyride.DailyRideApplication - Running with Spring Boot v3.2.0, Spring v6.1.1
2025-06-05 02:43:40 [main] INFO  com.dailyride.DailyRideApplication - The following 1 profile is active: "development"
2025-06-05 02:43:42 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8090 (http)
2025-06-05 02:43:42 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-05 02:43:42 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.16]
2025-06-05 02:43:42 [main] INFO  o.a.c.c.C.[.[localhost].[/api] - Initializing Spring embedded WebApplicationContext
2025-06-05 02:43:42 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2714 ms
2025-06-05 02:43:43 [main] DEBUG o.s.w.f.ServerHttpObservationFilter - Filter 'webMvcObservationFilter' configured for use
2025-06-05 02:43:44 [main] INFO  c.d.service.GoogleMapsService - 🗺️ Google Maps service initialized successfully
2025-06-05 02:43:44 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - 31 mappings in 'requestMappingHandlerMapping'
2025-06-05 02:43:44 [main] DEBUG o.s.w.s.h.SimpleUrlHandlerMapping - Patterns [/webjars/**, /**, /swagger-ui*/*swagger-initializer.js, /swagger-ui*/**] in 'resourceHandlerMapping'
2025-06-05 02:43:44 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerAdapter - ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
2025-06-05 02:43:44 [main] DEBUG o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - ControllerAdvice beans: 1 @ExceptionHandler, 1 ResponseBodyAdvice
2025-06-05 02:43:45 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 07594d90-24c5-4355-af0d-7c66e8e11103

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-06-05 02:43:45 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 3 endpoint(s) beneath base path '/actuator'
2025-06-05 02:43:45 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@11174bf, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@4f0c1409, org.springframework.security.web.context.SecurityContextHolderFilter@3ef396c9, org.springframework.security.web.header.HeaderWriterFilter@6f1163f7, org.springframework.web.filter.CorsFilter@188ae8d2, org.springframework.security.web.csrf.CsrfFilter@285c63cf, org.springframework.security.web.authentication.logout.LogoutFilter@578198d9, org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter@34ede267, org.springframework.security.web.authentication.ui.DefaultLoginPageGeneratingFilter@682d9f21, org.springframework.security.web.authentication.ui.DefaultLogoutPageGeneratingFilter@7a522157, org.springframework.security.web.authentication.www.BasicAuthenticationFilter@3e0e0ba7, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@4e0802e0, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@4a18e0f1, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@706c062e, org.springframework.security.web.access.ExceptionTranslationFilter@115a7e51, org.springframework.security.web.access.intercept.AuthorizationFilter@96dfcbb]
2025-06-05 02:43:45 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'
2025-06-05 02:43:45 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-06-05 02:43:45 [main] ERROR o.s.b.d.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 8090 was already in use.

Action:

Identify and stop the process that's listening on port 8090 or configure this application to listen on another port.

2025-06-05 02:50:28 [http-nio-8090-exec-2] INFO  o.a.c.c.C.[.[localhost].[/api] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-05 02:50:28 [http-nio-8090-exec-2] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-05 02:50:28 [http-nio-8090-exec-2] DEBUG o.s.web.servlet.DispatcherServlet - Detected StandardServletMultipartResolver
2025-06-05 02:50:28 [http-nio-8090-exec-2] DEBUG o.s.web.servlet.DispatcherServlet - Detected AcceptHeaderLocaleResolver
2025-06-05 02:50:28 [http-nio-8090-exec-2] DEBUG o.s.web.servlet.DispatcherServlet - Detected FixedThemeResolver
2025-06-05 02:50:28 [http-nio-8090-exec-2] DEBUG o.s.web.servlet.DispatcherServlet - Detected org.springframework.web.servlet.view.DefaultRequestToViewNameTranslator@426574b8
2025-06-05 02:50:28 [http-nio-8090-exec-2] DEBUG o.s.web.servlet.DispatcherServlet - Detected org.springframework.web.servlet.support.SessionFlashMapManager@7773142e
2025-06-05 02:50:28 [http-nio-8090-exec-2] DEBUG o.s.web.servlet.DispatcherServlet - enableLoggingRequestDetails='false': request parameters and headers will be masked to prevent unsafe logging of potentially sensitive data
2025-06-05 02:50:28 [http-nio-8090-exec-2] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 18 ms
2025-06-05 02:50:28 [http-nio-8090-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing GET /health
2025-06-05 02:50:28 [http-nio-8090-exec-2] WARN  o.s.w.s.h.HandlerMappingIntrospector - Cache miss for REQUEST dispatch to '/api/health' (previous null). Performing CorsConfiguration lookup. This is logged once only at WARN level, and every time at TRACE.
2025-06-05 02:50:28 [http-nio-8090-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-05 02:50:28 [http-nio-8090-exec-2] DEBUG o.s.s.w.s.HttpSessionRequestCache - Saved request http://localhost:8090/api/health?continue to session
2025-06-05 02:50:28 [http-nio-8090-exec-2] DEBUG o.s.s.w.a.DelegatingAuthenticationEntryPoint - Trying to match using And [Not [RequestHeaderRequestMatcher [expectedHeaderName=X-Requested-With, expectedHeaderValue=XMLHttpRequest]], MediaTypeRequestMatcher [contentNegotiationStrategy=org.springframework.web.accept.ContentNegotiationManager@4f7872e6, matchingMediaTypes=[application/xhtml+xml, image/*, text/html, text/plain], useEquals=false, ignoredMediaTypes=[*/*]]]
2025-06-05 02:50:28 [http-nio-8090-exec-2] DEBUG o.s.s.w.a.DelegatingAuthenticationEntryPoint - Trying to match using Or [RequestHeaderRequestMatcher [expectedHeaderName=X-Requested-With, expectedHeaderValue=XMLHttpRequest], And [Not [MediaTypeRequestMatcher [contentNegotiationStrategy=org.springframework.web.accept.ContentNegotiationManager@4f7872e6, matchingMediaTypes=[text/html], useEquals=false, ignoredMediaTypes=[]]], MediaTypeRequestMatcher [contentNegotiationStrategy=org.springframework.web.accept.ContentNegotiationManager@4f7872e6, matchingMediaTypes=[application/atom+xml, application/x-www-form-urlencoded, application/json, application/octet-stream, application/xml, multipart/form-data, text/xml], useEquals=false, ignoredMediaTypes=[*/*]]], MediaTypeRequestMatcher [contentNegotiationStrategy=org.springframework.web.accept.ContentNegotiationManager@4f7872e6, matchingMediaTypes=[*/*], useEquals=true, ignoredMediaTypes=[]]]
2025-06-05 02:50:28 [http-nio-8090-exec-2] DEBUG o.s.s.w.a.DelegatingAuthenticationEntryPoint - Match found! Executing org.springframework.security.web.authentication.DelegatingAuthenticationEntryPoint@6074d638
2025-06-05 02:50:28 [http-nio-8090-exec-2] DEBUG o.s.s.w.a.DelegatingAuthenticationEntryPoint - Trying to match using RequestHeaderRequestMatcher [expectedHeaderName=X-Requested-With, expectedHeaderValue=XMLHttpRequest]
2025-06-05 02:50:28 [http-nio-8090-exec-2] DEBUG o.s.s.w.a.DelegatingAuthenticationEntryPoint - No match found. Using default entry point org.springframework.security.web.authentication.www.BasicAuthenticationEntryPoint@33504033
2025-06-05 02:50:28 [http-nio-8090-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-06-05 02:50:28 [http-nio-8090-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-05 02:50:28 [http-nio-8090-exec-2] DEBUG o.s.s.w.s.HttpSessionRequestCache - Saved request http://localhost:8090/api/error?continue to session
2025-06-05 02:50:28 [http-nio-8090-exec-2] DEBUG o.s.s.w.a.DelegatingAuthenticationEntryPoint - Trying to match using And [Not [RequestHeaderRequestMatcher [expectedHeaderName=X-Requested-With, expectedHeaderValue=XMLHttpRequest]], MediaTypeRequestMatcher [contentNegotiationStrategy=org.springframework.web.accept.ContentNegotiationManager@4f7872e6, matchingMediaTypes=[application/xhtml+xml, image/*, text/html, text/plain], useEquals=false, ignoredMediaTypes=[*/*]]]
2025-06-05 02:50:28 [http-nio-8090-exec-2] DEBUG o.s.s.w.a.DelegatingAuthenticationEntryPoint - Trying to match using Or [RequestHeaderRequestMatcher [expectedHeaderName=X-Requested-With, expectedHeaderValue=XMLHttpRequest], And [Not [MediaTypeRequestMatcher [contentNegotiationStrategy=org.springframework.web.accept.ContentNegotiationManager@4f7872e6, matchingMediaTypes=[text/html], useEquals=false, ignoredMediaTypes=[]]], MediaTypeRequestMatcher [contentNegotiationStrategy=org.springframework.web.accept.ContentNegotiationManager@4f7872e6, matchingMediaTypes=[application/atom+xml, application/x-www-form-urlencoded, application/json, application/octet-stream, application/xml, multipart/form-data, text/xml], useEquals=false, ignoredMediaTypes=[*/*]]], MediaTypeRequestMatcher [contentNegotiationStrategy=org.springframework.web.accept.ContentNegotiationManager@4f7872e6, matchingMediaTypes=[*/*], useEquals=true, ignoredMediaTypes=[]]]
2025-06-05 02:50:28 [http-nio-8090-exec-2] DEBUG o.s.s.w.a.DelegatingAuthenticationEntryPoint - Match found! Executing org.springframework.security.web.authentication.DelegatingAuthenticationEntryPoint@6074d638
2025-06-05 02:50:28 [http-nio-8090-exec-2] DEBUG o.s.s.w.a.DelegatingAuthenticationEntryPoint - Trying to match using RequestHeaderRequestMatcher [expectedHeaderName=X-Requested-With, expectedHeaderValue=XMLHttpRequest]
2025-06-05 02:50:28 [http-nio-8090-exec-2] DEBUG o.s.s.w.a.DelegatingAuthenticationEntryPoint - No match found. Using default entry point org.springframework.security.web.authentication.www.BasicAuthenticationEntryPoint@33504033
2025-06-05 02:50:35 [http-nio-8090-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing GET /health
2025-06-05 02:50:35 [http-nio-8090-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-05 02:50:35 [http-nio-8090-exec-1] DEBUG o.s.s.w.s.HttpSessionRequestCache - Saved request http://localhost:8090/api/health?continue to session
2025-06-05 02:50:35 [http-nio-8090-exec-1] DEBUG o.s.s.w.a.DelegatingAuthenticationEntryPoint - Trying to match using And [Not [RequestHeaderRequestMatcher [expectedHeaderName=X-Requested-With, expectedHeaderValue=XMLHttpRequest]], MediaTypeRequestMatcher [contentNegotiationStrategy=org.springframework.web.accept.ContentNegotiationManager@4f7872e6, matchingMediaTypes=[application/xhtml+xml, image/*, text/html, text/plain], useEquals=false, ignoredMediaTypes=[*/*]]]
2025-06-05 02:50:35 [http-nio-8090-exec-1] DEBUG o.s.s.w.a.DelegatingAuthenticationEntryPoint - Trying to match using Or [RequestHeaderRequestMatcher [expectedHeaderName=X-Requested-With, expectedHeaderValue=XMLHttpRequest], And [Not [MediaTypeRequestMatcher [contentNegotiationStrategy=org.springframework.web.accept.ContentNegotiationManager@4f7872e6, matchingMediaTypes=[text/html], useEquals=false, ignoredMediaTypes=[]]], MediaTypeRequestMatcher [contentNegotiationStrategy=org.springframework.web.accept.ContentNegotiationManager@4f7872e6, matchingMediaTypes=[application/atom+xml, application/x-www-form-urlencoded, application/json, application/octet-stream, application/xml, multipart/form-data, text/xml], useEquals=false, ignoredMediaTypes=[*/*]]], MediaTypeRequestMatcher [contentNegotiationStrategy=org.springframework.web.accept.ContentNegotiationManager@4f7872e6, matchingMediaTypes=[*/*], useEquals=true, ignoredMediaTypes=[]]]
2025-06-05 02:50:35 [http-nio-8090-exec-1] DEBUG o.s.s.w.a.DelegatingAuthenticationEntryPoint - Match found! Executing org.springframework.security.web.authentication.DelegatingAuthenticationEntryPoint@6074d638
2025-06-05 02:50:35 [http-nio-8090-exec-1] DEBUG o.s.s.w.a.DelegatingAuthenticationEntryPoint - Trying to match using RequestHeaderRequestMatcher [expectedHeaderName=X-Requested-With, expectedHeaderValue=XMLHttpRequest]
2025-06-05 02:50:35 [http-nio-8090-exec-1] DEBUG o.s.s.w.a.DelegatingAuthenticationEntryPoint - No match found. Using default entry point org.springframework.security.web.authentication.www.BasicAuthenticationEntryPoint@33504033
2025-06-05 02:50:35 [http-nio-8090-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-06-05 02:50:35 [http-nio-8090-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-05 02:50:35 [http-nio-8090-exec-1] DEBUG o.s.s.w.s.HttpSessionRequestCache - Saved request http://localhost:8090/api/error?continue to session
2025-06-05 02:50:35 [http-nio-8090-exec-1] DEBUG o.s.s.w.a.DelegatingAuthenticationEntryPoint - Trying to match using And [Not [RequestHeaderRequestMatcher [expectedHeaderName=X-Requested-With, expectedHeaderValue=XMLHttpRequest]], MediaTypeRequestMatcher [contentNegotiationStrategy=org.springframework.web.accept.ContentNegotiationManager@4f7872e6, matchingMediaTypes=[application/xhtml+xml, image/*, text/html, text/plain], useEquals=false, ignoredMediaTypes=[*/*]]]
2025-06-05 02:50:35 [http-nio-8090-exec-1] DEBUG o.s.s.w.a.DelegatingAuthenticationEntryPoint - Trying to match using Or [RequestHeaderRequestMatcher [expectedHeaderName=X-Requested-With, expectedHeaderValue=XMLHttpRequest], And [Not [MediaTypeRequestMatcher [contentNegotiationStrategy=org.springframework.web.accept.ContentNegotiationManager@4f7872e6, matchingMediaTypes=[text/html], useEquals=false, ignoredMediaTypes=[]]], MediaTypeRequestMatcher [contentNegotiationStrategy=org.springframework.web.accept.ContentNegotiationManager@4f7872e6, matchingMediaTypes=[application/atom+xml, application/x-www-form-urlencoded, application/json, application/octet-stream, application/xml, multipart/form-data, text/xml], useEquals=false, ignoredMediaTypes=[*/*]]], MediaTypeRequestMatcher [contentNegotiationStrategy=org.springframework.web.accept.ContentNegotiationManager@4f7872e6, matchingMediaTypes=[*/*], useEquals=true, ignoredMediaTypes=[]]]
2025-06-05 02:50:35 [http-nio-8090-exec-1] DEBUG o.s.s.w.a.DelegatingAuthenticationEntryPoint - Match found! Executing org.springframework.security.web.authentication.DelegatingAuthenticationEntryPoint@6074d638
2025-06-05 02:50:35 [http-nio-8090-exec-1] DEBUG o.s.s.w.a.DelegatingAuthenticationEntryPoint - Trying to match using RequestHeaderRequestMatcher [expectedHeaderName=X-Requested-With, expectedHeaderValue=XMLHttpRequest]
2025-06-05 02:50:35 [http-nio-8090-exec-1] DEBUG o.s.s.w.a.DelegatingAuthenticationEntryPoint - No match found. Using default entry point org.springframework.security.web.authentication.www.BasicAuthenticationEntryPoint@33504033
2025-06-05 03:32:09 [main] INFO  com.dailyride.DailyRideApplication - Starting DailyRideApplication v1.0.0 using Java 21.0.7 with PID 3772 (C:\Users\<USER>\Downloads\dailyrRide\dailyrRide\backend\target\dailyride-backend-1.0.0.jar started by medad in C:\Users\<USER>\Downloads\dailyrRide\dailyrRide\backend)
2025-06-05 03:32:09 [main] DEBUG com.dailyride.DailyRideApplication - Running with Spring Boot v3.2.0, Spring v6.1.1
2025-06-05 03:32:09 [main] INFO  com.dailyride.DailyRideApplication - The following 1 profile is active: "development"
2025-06-05 03:32:12 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8090 (http)
2025-06-05 03:32:12 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-05 03:32:12 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.16]
2025-06-05 03:32:12 [main] INFO  o.a.c.c.C.[.[localhost].[/api] - Initializing Spring embedded WebApplicationContext
2025-06-05 03:32:12 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2822 ms
2025-06-05 03:32:12 [main] DEBUG o.s.w.f.ServerHttpObservationFilter - Filter 'webMvcObservationFilter' configured for use
2025-06-05 03:32:14 [main] INFO  c.d.service.GoogleMapsService - 🗺️ Google Maps service initialized successfully
2025-06-05 03:32:15 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - 38 mappings in 'requestMappingHandlerMapping'
2025-06-05 03:32:15 [main] DEBUG o.s.w.s.h.SimpleUrlHandlerMapping - Patterns [/webjars/**, /**, /swagger-ui*/*swagger-initializer.js, /swagger-ui*/**] in 'resourceHandlerMapping'
2025-06-05 03:32:15 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerAdapter - ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
2025-06-05 03:32:15 [main] DEBUG o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - ControllerAdvice beans: 1 @ExceptionHandler, 1 ResponseBodyAdvice
2025-06-05 03:32:15 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 09c47baf-b58e-4f48-8486-4e8d1c8f648a

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-06-05 03:32:15 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 3 endpoint(s) beneath base path '/actuator'
2025-06-05 03:32:15 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@1d0f7bcf, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@d0e4972, org.springframework.security.web.context.SecurityContextHolderFilter@5dfec221, org.springframework.security.web.header.HeaderWriterFilter@53a16dd6, org.springframework.web.filter.CorsFilter@1978b0d5, org.springframework.security.web.csrf.CsrfFilter@3ae87e38, org.springframework.security.web.authentication.logout.LogoutFilter@280099a0, org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter@1adfb5b8, org.springframework.security.web.authentication.ui.DefaultLoginPageGeneratingFilter@2e7e84f8, org.springframework.security.web.authentication.ui.DefaultLogoutPageGeneratingFilter@6556471b, org.springframework.security.web.authentication.www.BasicAuthenticationFilter@1610c03c, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@7bc8da3f, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@7978550b, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@3e1897d, org.springframework.security.web.access.ExceptionTranslationFilter@c96c497, org.springframework.security.web.access.intercept.AuthorizationFilter@7f792530]
2025-06-05 03:32:15 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8090 (http) with context path '/api'
2025-06-05 03:32:15 [main] INFO  com.dailyride.DailyRideApplication - Started DailyRideApplication in 7.048 seconds (process running for 8.073)
2025-06-05 08:24:41 [main] INFO  com.dailyride.DailyRideApplication - Starting DailyRideApplication using Java 21.0.7 with PID 19724 (C:\Users\<USER>\Downloads\dailyrRide\dailyrRide\backend\target\classes started by medad in C:\Users\<USER>\Downloads\dailyrRide\dailyrRide\backend)
2025-06-05 08:24:41 [main] DEBUG com.dailyride.DailyRideApplication - Running with Spring Boot v3.2.0, Spring v6.1.1
2025-06-05 08:24:41 [main] INFO  com.dailyride.DailyRideApplication - The following 1 profile is active: "development"
2025-06-05 08:24:42 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8090 (http)
2025-06-05 08:24:42 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-05 08:24:42 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.16]
2025-06-05 08:24:42 [main] INFO  o.a.c.c.C.[.[localhost].[/api] - Initializing Spring embedded WebApplicationContext
2025-06-05 08:24:42 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1863 ms
2025-06-05 08:24:43 [main] DEBUG o.s.w.f.ServerHttpObservationFilter - Filter 'webMvcObservationFilter' configured for use
2025-06-05 08:24:44 [main] INFO  c.d.service.GoogleMapsService - 🗺️ Google Maps service initialized successfully
2025-06-05 08:24:44 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - 38 mappings in 'requestMappingHandlerMapping'
2025-06-05 08:24:44 [main] DEBUG o.s.w.s.h.SimpleUrlHandlerMapping - Patterns [/webjars/**, /**, /swagger-ui*/*swagger-initializer.js, /swagger-ui*/**] in 'resourceHandlerMapping'
2025-06-05 08:24:44 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerAdapter - ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
2025-06-05 08:24:44 [main] DEBUG o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - ControllerAdvice beans: 1 @ExceptionHandler, 1 ResponseBodyAdvice
2025-06-05 08:24:45 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: cac6a761-10b9-40d5-b5a2-0205c89c7470

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-06-05 08:24:45 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 3 endpoint(s) beneath base path '/actuator'
2025-06-05 08:24:45 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@6b92a0d1, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@4b9ed99d, org.springframework.security.web.context.SecurityContextHolderFilter@2e64ae1a, org.springframework.security.web.header.HeaderWriterFilter@7646c9f5, org.springframework.web.filter.CorsFilter@39b95a80, org.springframework.security.web.csrf.CsrfFilter@2a8e30e3, org.springframework.security.web.authentication.logout.LogoutFilter@31b289da, org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter@52e92f6, org.springframework.security.web.authentication.ui.DefaultLoginPageGeneratingFilter@6fad5ace, org.springframework.security.web.authentication.ui.DefaultLogoutPageGeneratingFilter@3ec62141, org.springframework.security.web.authentication.www.BasicAuthenticationFilter@5feff876, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@30a20fb3, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@34f24a11, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@402c9a60, org.springframework.security.web.access.ExceptionTranslationFilter@687f62a5, org.springframework.security.web.access.intercept.AuthorizationFilter@2b82018]
2025-06-05 08:24:45 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8090 (http) with context path '/api'
2025-06-05 08:24:45 [main] INFO  com.dailyride.DailyRideApplication - Started DailyRideApplication in 4.873 seconds (process running for 5.41)
2025-06-05 09:17:00 [main] INFO  com.dailyride.DailyRideApplication - Starting DailyRideApplication using Java 21.0.7 with PID 6484 (C:\Users\<USER>\Downloads\dailyrRide\dailyrRide\backend\target\classes started by medad in C:\Users\<USER>\Downloads\dailyrRide\dailyrRide\backend)
2025-06-05 09:17:00 [main] DEBUG com.dailyride.DailyRideApplication - Running with Spring Boot v3.2.0, Spring v6.1.1
2025-06-05 09:17:00 [main] INFO  com.dailyride.DailyRideApplication - The following 1 profile is active: "development"
2025-06-05 09:17:01 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8090 (http)
2025-06-05 09:17:01 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-05 09:17:01 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.16]
2025-06-05 09:17:02 [main] INFO  o.a.c.c.C.[.[localhost].[/api] - Initializing Spring embedded WebApplicationContext
2025-06-05 09:17:02 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1847 ms
2025-06-05 09:17:02 [main] DEBUG o.s.w.f.ServerHttpObservationFilter - Filter 'webMvcObservationFilter' configured for use
2025-06-05 09:17:03 [main] INFO  c.d.service.GoogleMapsService - 🗺️ Google Maps service initialized successfully
2025-06-05 09:17:03 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - 38 mappings in 'requestMappingHandlerMapping'
2025-06-05 09:17:04 [main] DEBUG o.s.w.s.h.SimpleUrlHandlerMapping - Patterns [/webjars/**, /**, /swagger-ui*/*swagger-initializer.js, /swagger-ui*/**] in 'resourceHandlerMapping'
2025-06-05 09:17:04 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerAdapter - ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
2025-06-05 09:17:04 [main] DEBUG o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - ControllerAdvice beans: 1 @ExceptionHandler, 1 ResponseBodyAdvice
2025-06-05 09:17:04 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 57afb677-80b2-47f9-855f-78dfda96790d

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-06-05 09:17:04 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 3 endpoint(s) beneath base path '/actuator'
2025-06-05 09:17:04 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@4f0c1409, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@188ae8d2, org.springframework.security.web.context.SecurityContextHolderFilter@4ca907af, org.springframework.security.web.header.HeaderWriterFilter@23ea3853, org.springframework.web.filter.CorsFilter@7a522157, org.springframework.security.web.csrf.CsrfFilter@e27d97b, org.springframework.security.web.authentication.logout.LogoutFilter@63ed5dae, org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter@6bf77ee, org.springframework.security.web.authentication.ui.DefaultLoginPageGeneratingFilter@7b54a0a4, org.springframework.security.web.authentication.ui.DefaultLogoutPageGeneratingFilter@706c062e, org.springframework.security.web.authentication.www.BasicAuthenticationFilter@7df5549e, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@5a99543b, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@4e0802e0, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@5feff876, org.springframework.security.web.access.ExceptionTranslationFilter@d1d85d0, org.springframework.security.web.access.intercept.AuthorizationFilter@34ede267]
2025-06-05 09:17:04 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8090 (http) with context path '/api'
2025-06-05 09:17:04 [main] INFO  com.dailyride.DailyRideApplication - Started DailyRideApplication in 5.122 seconds (process running for 5.676)
2025-06-05 09:26:46 [main] INFO  com.dailyride.DailyRideApplication - Starting DailyRideApplication using Java 21.0.7 with PID 5752 (C:\Users\<USER>\Downloads\dailyrRide\dailyrRide\backend\target\classes started by medad in C:\Users\<USER>\Downloads\dailyrRide\dailyrRide\backend)
2025-06-05 09:26:46 [main] DEBUG com.dailyride.DailyRideApplication - Running with Spring Boot v3.2.0, Spring v6.1.1
2025-06-05 09:26:46 [main] INFO  com.dailyride.DailyRideApplication - The following 1 profile is active: "development"
2025-06-05 09:26:48 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8090 (http)
2025-06-05 09:26:48 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-05 09:26:48 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.16]
2025-06-05 09:26:48 [main] INFO  o.a.c.c.C.[.[localhost].[/api] - Initializing Spring embedded WebApplicationContext
2025-06-05 09:26:48 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1605 ms
2025-06-05 09:26:48 [main] DEBUG o.s.w.f.ServerHttpObservationFilter - Filter 'webMvcObservationFilter' configured for use
2025-06-05 09:26:49 [main] INFO  c.d.service.GoogleMapsService - 🗺️ Google Maps service initialized successfully
2025-06-05 09:26:49 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - 38 mappings in 'requestMappingHandlerMapping'
2025-06-05 09:26:49 [main] DEBUG o.s.w.s.h.SimpleUrlHandlerMapping - Patterns [/webjars/**, /**, /swagger-ui*/*swagger-initializer.js, /swagger-ui*/**] in 'resourceHandlerMapping'
2025-06-05 09:26:49 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerAdapter - ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
2025-06-05 09:26:50 [main] DEBUG o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - ControllerAdvice beans: 1 @ExceptionHandler, 1 ResponseBodyAdvice
2025-06-05 09:26:50 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 1a3f14a4-52dc-4998-be84-c493570feffd

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-06-05 09:26:50 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 3 endpoint(s) beneath base path '/actuator'
2025-06-05 09:26:50 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@6b92a0d1, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@4b9ed99d, org.springframework.security.web.context.SecurityContextHolderFilter@2e64ae1a, org.springframework.security.web.header.HeaderWriterFilter@7646c9f5, org.springframework.web.filter.CorsFilter@39b95a80, org.springframework.security.web.csrf.CsrfFilter@2a8e30e3, org.springframework.security.web.authentication.logout.LogoutFilter@31b289da, org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter@52e92f6, org.springframework.security.web.authentication.ui.DefaultLoginPageGeneratingFilter@6fad5ace, org.springframework.security.web.authentication.ui.DefaultLogoutPageGeneratingFilter@3ec62141, org.springframework.security.web.authentication.www.BasicAuthenticationFilter@5feff876, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@30a20fb3, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@34f24a11, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@402c9a60, org.springframework.security.web.access.ExceptionTranslationFilter@687f62a5, org.springframework.security.web.access.intercept.AuthorizationFilter@2b82018]
2025-06-05 09:26:50 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8090 (http) with context path '/api'
2025-06-05 09:26:50 [main] INFO  com.dailyride.DailyRideApplication - Started DailyRideApplication in 4.216 seconds (process running for 4.701)
2025-06-05 12:31:26 [main] INFO  com.dailyride.DailyRideApplication - Starting DailyRideApplication using Java 21.0.7 with PID 12428 (C:\Users\<USER>\Downloads\dailyrRide\dailyrRide\backend\target\classes started by medad in C:\Users\<USER>\Downloads\dailyrRide\dailyrRide\backend)
2025-06-05 12:31:26 [main] DEBUG com.dailyride.DailyRideApplication - Running with Spring Boot v3.2.0, Spring v6.1.1
2025-06-05 12:31:26 [main] INFO  com.dailyride.DailyRideApplication - The following 1 profile is active: "development"
2025-06-05 12:31:27 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8090 (http)
2025-06-05 12:31:27 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-05 12:31:27 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.16]
2025-06-05 12:31:27 [main] INFO  o.a.c.c.C.[.[localhost].[/api] - Initializing Spring embedded WebApplicationContext
2025-06-05 12:31:27 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1648 ms
2025-06-05 12:31:27 [main] DEBUG o.s.w.f.ServerHttpObservationFilter - Filter 'webMvcObservationFilter' configured for use
2025-06-05 12:31:28 [main] INFO  c.d.service.GoogleMapsService - 🗺️ Google Maps service initialized successfully
2025-06-05 12:31:29 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - 38 mappings in 'requestMappingHandlerMapping'
2025-06-05 12:31:29 [main] DEBUG o.s.w.s.h.SimpleUrlHandlerMapping - Patterns [/webjars/**, /**, /swagger-ui*/*swagger-initializer.js, /swagger-ui*/**] in 'resourceHandlerMapping'
2025-06-05 12:31:29 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerAdapter - ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
2025-06-05 12:31:29 [main] DEBUG o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - ControllerAdvice beans: 1 @ExceptionHandler, 1 ResponseBodyAdvice
2025-06-05 12:31:29 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: b2fd1568-5266-4637-b431-f09c9276cbf1

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-06-05 12:31:29 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 3 endpoint(s) beneath base path '/actuator'
2025-06-05 12:31:29 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@6b92a0d1, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@4b9ed99d, org.springframework.security.web.context.SecurityContextHolderFilter@2e64ae1a, org.springframework.security.web.header.HeaderWriterFilter@7646c9f5, org.springframework.web.filter.CorsFilter@39b95a80, org.springframework.security.web.csrf.CsrfFilter@2a8e30e3, org.springframework.security.web.authentication.logout.LogoutFilter@31b289da, org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter@52e92f6, org.springframework.security.web.authentication.ui.DefaultLoginPageGeneratingFilter@6fad5ace, org.springframework.security.web.authentication.ui.DefaultLogoutPageGeneratingFilter@3ec62141, org.springframework.security.web.authentication.www.BasicAuthenticationFilter@5feff876, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@30a20fb3, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@34f24a11, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@402c9a60, org.springframework.security.web.access.ExceptionTranslationFilter@687f62a5, org.springframework.security.web.access.intercept.AuthorizationFilter@2b82018]
2025-06-05 12:31:29 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8090 (http) with context path '/api'
2025-06-05 12:31:29 [main] INFO  com.dailyride.DailyRideApplication - Started DailyRideApplication in 4.318 seconds (process running for 4.822)
2025-06-05 18:31:36 [main] INFO  com.dailyride.DailyRideApplication - Starting DailyRideApplication using Java 21.0.7 with PID 19468 (C:\Users\<USER>\Downloads\dailyrRide\dailyrRide\backend\target\classes started by medad in C:\Users\<USER>\Downloads\dailyrRide\dailyrRide\backend)
2025-06-05 18:31:36 [main] DEBUG com.dailyride.DailyRideApplication - Running with Spring Boot v3.2.0, Spring v6.1.1
2025-06-05 18:31:36 [main] INFO  com.dailyride.DailyRideApplication - The following 1 profile is active: "development"
2025-06-05 18:31:38 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8090 (http)
2025-06-05 18:31:38 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-05 18:31:38 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.16]
2025-06-05 18:31:38 [main] INFO  o.a.c.c.C.[.[localhost].[/api] - Initializing Spring embedded WebApplicationContext
2025-06-05 18:31:38 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1989 ms
2025-06-05 18:31:39 [main] DEBUG o.s.w.f.ServerHttpObservationFilter - Filter 'webMvcObservationFilter' configured for use
2025-06-05 18:31:40 [main] INFO  c.d.service.GoogleMapsService - 🗺️ Google Maps service initialized successfully
2025-06-05 18:31:40 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - 38 mappings in 'requestMappingHandlerMapping'
2025-06-05 18:31:40 [main] DEBUG o.s.w.s.h.SimpleUrlHandlerMapping - Patterns [/webjars/**, /**, /swagger-ui*/*swagger-initializer.js, /swagger-ui*/**] in 'resourceHandlerMapping'
2025-06-05 18:31:40 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerAdapter - ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
2025-06-05 18:31:40 [main] DEBUG o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - ControllerAdvice beans: 1 @ExceptionHandler, 1 ResponseBodyAdvice
2025-06-05 18:31:41 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: af567698-7970-4a5d-8244-0390da03fbb9

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-06-05 18:31:41 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 3 endpoint(s) beneath base path '/actuator'
2025-06-05 18:31:41 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@6b92a0d1, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@4b9ed99d, org.springframework.security.web.context.SecurityContextHolderFilter@2e64ae1a, org.springframework.security.web.header.HeaderWriterFilter@7646c9f5, org.springframework.web.filter.CorsFilter@39b95a80, org.springframework.security.web.csrf.CsrfFilter@2a8e30e3, org.springframework.security.web.authentication.logout.LogoutFilter@31b289da, org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter@52e92f6, org.springframework.security.web.authentication.ui.DefaultLoginPageGeneratingFilter@6fad5ace, org.springframework.security.web.authentication.ui.DefaultLogoutPageGeneratingFilter@3ec62141, org.springframework.security.web.authentication.www.BasicAuthenticationFilter@5feff876, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@30a20fb3, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@34f24a11, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@402c9a60, org.springframework.security.web.access.ExceptionTranslationFilter@687f62a5, org.springframework.security.web.access.intercept.AuthorizationFilter@2b82018]
2025-06-05 18:31:41 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8090 (http) with context path '/api'
2025-06-05 18:31:41 [main] INFO  com.dailyride.DailyRideApplication - Started DailyRideApplication in 5.29 seconds (process running for 5.889)
2025-06-05 18:47:41 [main] INFO  com.dailyride.DailyRideApplication - Starting DailyRideApplication using Java 21.0.7 with PID 14720 (C:\Users\<USER>\Downloads\dailyrRide\dailyrRide\backend\target\classes started by medad in C:\Users\<USER>\Downloads\dailyrRide\dailyrRide\backend)
2025-06-05 18:47:41 [main] DEBUG com.dailyride.DailyRideApplication - Running with Spring Boot v3.2.0, Spring v6.1.1
2025-06-05 18:47:41 [main] INFO  com.dailyride.DailyRideApplication - The following 1 profile is active: "development"
2025-06-05 18:47:42 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8090 (http)
2025-06-05 18:47:42 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-05 18:47:42 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.16]
2025-06-05 18:47:42 [main] INFO  o.a.c.c.C.[.[localhost].[/api] - Initializing Spring embedded WebApplicationContext
2025-06-05 18:47:42 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1496 ms
2025-06-05 18:47:43 [main] DEBUG o.s.w.f.ServerHttpObservationFilter - Filter 'webMvcObservationFilter' configured for use
2025-06-05 18:47:43 [main] INFO  c.d.service.GoogleMapsService - 🗺️ Google Maps service initialized successfully
2025-06-05 18:47:44 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - 38 mappings in 'requestMappingHandlerMapping'
2025-06-05 18:47:44 [main] DEBUG o.s.w.s.h.SimpleUrlHandlerMapping - Patterns [/webjars/**, /**, /swagger-ui*/*swagger-initializer.js, /swagger-ui*/**] in 'resourceHandlerMapping'
2025-06-05 18:47:44 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerAdapter - ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
2025-06-05 18:47:44 [main] DEBUG o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - ControllerAdvice beans: 1 @ExceptionHandler, 1 ResponseBodyAdvice
2025-06-05 18:47:44 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 1c22af16-1c76-4041-88d3-98d74b79c27c

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-06-05 18:47:44 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 3 endpoint(s) beneath base path '/actuator'
2025-06-05 18:47:44 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@6b92a0d1, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@4b9ed99d, org.springframework.security.web.context.SecurityContextHolderFilter@2e64ae1a, org.springframework.security.web.header.HeaderWriterFilter@7646c9f5, org.springframework.web.filter.CorsFilter@39b95a80, org.springframework.security.web.csrf.CsrfFilter@2a8e30e3, org.springframework.security.web.authentication.logout.LogoutFilter@31b289da, org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter@52e92f6, org.springframework.security.web.authentication.ui.DefaultLoginPageGeneratingFilter@6fad5ace, org.springframework.security.web.authentication.ui.DefaultLogoutPageGeneratingFilter@3ec62141, org.springframework.security.web.authentication.www.BasicAuthenticationFilter@5feff876, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@30a20fb3, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@34f24a11, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@402c9a60, org.springframework.security.web.access.ExceptionTranslationFilter@687f62a5, org.springframework.security.web.access.intercept.AuthorizationFilter@2b82018]
2025-06-05 18:47:44 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8090 (http) with context path '/api'
2025-06-05 18:47:44 [main] INFO  com.dailyride.DailyRideApplication - Started DailyRideApplication in 4.013 seconds (process running for 4.509)
