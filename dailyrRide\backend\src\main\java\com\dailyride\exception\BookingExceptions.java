package com.dailyride.exception;

/**
 * Booking-related exception classes.
 */
public class BookingExceptions {
    
    public static class BookingNotFoundException extends DailyRideException {
        public BookingNotFoundException(String bookingId) {
            super("Booking not found with ID: " + bookingId, "BOOKING_NOT_FOUND");
        }
    }
    
    public static class DuplicateBookingException extends DailyRideException {
        public DuplicateBookingException() {
            super("You have already booked this trip", "DUPLICATE_BOOKING");
        }
    }
    
    public static class BookingNotCancellableException extends DailyRideException {
        public BookingNotCancellableException(String reason) {
            super("Booking cannot be cancelled: " + reason, "BOOKING_NOT_CANCELLABLE");
        }
    }
    
    public static class InvalidBookingStatusException extends DailyRideException {
        public InvalidBookingStatusException(String currentStatus, String requestedStatus) {
            super(String.format("Cannot change booking status from %s to %s", 
                  currentStatus, requestedStatus), "INVALID_BOOKING_STATUS");
        }
    }
    
    public static class PaymentProcessingException extends DailyRideException {
        public PaymentProcessingException(String message) {
            super("Payment processing failed: " + message, "PAYMENT_PROCESSING_ERROR");
        }
    }
    
    public static class RefundProcessingException extends DailyRideException {
        public RefundProcessingException(String message) {
            super("Refund processing failed: " + message, "REFUND_PROCESSING_ERROR");
        }
    }
    
    public static class UnauthorizedBookingAccessException extends DailyRideException {
        public UnauthorizedBookingAccessException() {
            super("You are not authorized to access this booking", "UNAUTHORIZED_BOOKING_ACCESS");
        }
    }
    
    public static class BookingDeadlineExceededException extends DailyRideException {
        public BookingDeadlineExceededException() {
            super("Booking deadline has been exceeded", "BOOKING_DEADLINE_EXCEEDED");
        }
    }
}
