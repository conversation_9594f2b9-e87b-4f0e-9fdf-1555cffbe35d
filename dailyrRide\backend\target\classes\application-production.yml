# No database configuration needed for Firestore

# Production JWT settings
jwt:
  secret: ${JWT_SECRET}
  expiration: ${JWT_EXPIRATION:604800000} # 7 days

# Production logging
logging:
  level:
    com.dailyride: INFO
    org.springframework.security: WARN
    org.hibernate.SQL: WARN
  file:
    name: /var/log/dailyride/application.log

# Strict CORS in production
cors:
  allowed-origins: ${CORS_ALLOWED_ORIGINS}

# Production file upload
file:
  upload:
    path: ${UPLOAD_PATH:/var/uploads/dailyride/}

# Production actuator settings
management:
  endpoints:
    web:
      exposure:
        include: health,info
  endpoint:
    health:
      show-details: never
