package com.dailyride.exception;

/**
 * Trip-related exception classes.
 */
public class TripExceptions {
    
    public static class TripNotFoundException extends DailyRideException {
        public TripNotFoundException(String tripId) {
            super("Trip not found with ID: " + tripId, "TRIP_NOT_FOUND");
        }
    }
    
    public static class TripNotBookableException extends DailyRideException {
        public TripNotBookableException(String reason) {
            super("Trip is not bookable: " + reason, "TRIP_NOT_BOOKABLE");
        }
    }
    
    public static class InsufficientSeatsException extends DailyRideException {
        public InsufficientSeatsException(int requestedSeats, int availableSeats) {
            super(String.format("Insufficient seats available. Requested: %d, Available: %d", 
                  requestedSeats, availableSeats), "INSUFFICIENT_SEATS");
        }
    }
    
    public static class TripAlreadyStartedException extends DailyRideException {
        public TripAlreadyStartedException() {
            super("Cannot modify trip that has already started", "TRIP_ALREADY_STARTED");
        }
    }
    
    public static class InvalidTripStatusException extends DailyRideException {
        public InvalidTripStatusException(String currentStatus, String requestedStatus) {
            super(String.format("Cannot change trip status from %s to %s", 
                  currentStatus, requestedStatus), "INVALID_TRIP_STATUS");
        }
    }
    
    public static class UnauthorizedTripAccessException extends DailyRideException {
        public UnauthorizedTripAccessException() {
            super("You are not authorized to access this trip", "UNAUTHORIZED_TRIP_ACCESS");
        }
    }
    
    public static class InvalidTripDataException extends DailyRideException {
        public InvalidTripDataException(String message) {
            super("Invalid trip data: " + message, "INVALID_TRIP_DATA");
        }
    }
}
