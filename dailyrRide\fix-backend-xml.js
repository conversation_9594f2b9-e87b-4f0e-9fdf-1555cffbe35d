#!/usr/bin/env node

/**
 * 🔧 Fix Backend XML Issues
 * Fixes malformed XML tags in pom.xml
 */

const fs = require('fs');
const path = require('path');

console.log('🔧 Fixing Backend XML Issues...');

const pomPath = './backend/pom.xml';

try {
  if (!fs.existsSync(pomPath)) {
    console.log('❌ pom.xml not found at:', pomPath);
    process.exit(1);
  }

  let pomContent = fs.readFileSync(pomPath, 'utf8');
  
  // Fix malformed <n> tag
  if (pomContent.includes('<n>DailyRide Backend</n>')) {
    console.log('🔍 Found malformed <n> tag, fixing...');
    pomContent = pomContent.replace('<n>DailyRide Backend</n>', '<name>DailyRide Backend</name>');
    
    // Write back the fixed content
    fs.writeFileSync(pomPath, pomContent, 'utf8');
    console.log('✅ Fixed malformed XML tag in pom.xml');
  } else {
    console.log('✅ No XML issues found in pom.xml');
  }

  // Validate the fix
  if (pomContent.includes('<name>DailyRide Backend</name>')) {
    console.log('✅ XML validation passed');
  } else {
    console.log('⚠️ XML validation failed - manual review needed');
  }

} catch (error) {
  console.error('❌ Error fixing XML:', error.message);
  process.exit(1);
}

console.log('🎉 Backend XML fix completed!');
