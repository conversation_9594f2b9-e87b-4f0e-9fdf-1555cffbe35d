# Firebase Setup Guide for DailyRide Backend

## Current Configuration Status
✅ **Project ID**: `dailydrive-892d3` (configured)
✅ **Client ID**: `************` (configured)
❌ **Service Account Credentials**: Missing (required for backend)

## Step 1: Get Firebase Service Account Key

1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Select project: **dailydrive-892d3**
3. Click gear icon ⚙️ → **Project Settings**
4. Go to **Service Accounts** tab
5. Click **Generate New Private Key**
6. Download the JSON file

## Step 2: Extract Credentials from Downloaded JSON

The downloaded JSON will look like this:
```json
{
  "type": "service_account",
  "project_id": "dailydrive-892d3",
  "private_key_id": "abc123...",
  "private_key": "-----BEGIN PRIVATE KEY-----\nMIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQC...\n-----<PERSON><PERSON> PRIVATE KEY-----\n",
  "client_email": "<EMAIL>",
  "client_id": "*********...",
  "auth_uri": "https://accounts.google.com/o/oauth2/auth",
  "token_uri": "https://oauth2.googleapis.com/token"
}
```

## Step 3: Set Environment Variables

### Option A: Create .env file in backend directory
```bash
# Firebase Configuration
FIREBASE_PROJECT_ID=dailydrive-892d3
FIREBASE_PRIVATE_KEY_ID=your_private_key_id_from_json
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nYOUR_PRIVATE_KEY_HERE\n-----END PRIVATE KEY-----\n"
FIREBASE_CLIENT_EMAIL=<EMAIL>
FIREBASE_CLIENT_ID=************

# Google Maps (already configured)
GOOGLE_MAPS_API_KEY=AIzaSyCtRoF_KnO_3F_n_4lBUnfkRpnAOL38EJc
```

### Option B: Set in PowerShell (Windows)
```powershell
$env:FIREBASE_PROJECT_ID="dailydrive-892d3"
$env:FIREBASE_PRIVATE_KEY_ID="your_private_key_id_from_json"
$env:FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----`nYOUR_PRIVATE_KEY_HERE`n-----END PRIVATE KEY-----`n"
$env:FIREBASE_CLIENT_EMAIL="<EMAIL>"
$env:FIREBASE_CLIENT_ID="************"
```

### Option C: Set in Command Prompt (Windows)
```cmd
set FIREBASE_PROJECT_ID=dailydrive-892d3
set FIREBASE_PRIVATE_KEY_ID=your_private_key_id_from_json
set FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nYOUR_PRIVATE_KEY_HERE\n-----END PRIVATE KEY-----\n"
set FIREBASE_CLIENT_EMAIL=<EMAIL>
set FIREBASE_CLIENT_ID=************
```

## Step 4: Restart the Application

After setting the environment variables:
```bash
cd backend
mvn spring-boot:run
```

## Step 5: Verify Firebase Connection

Once running, check the logs for:
```
🔥 Firebase initialized successfully for project: dailydrive-892d3
```

Instead of:
```
⚠️ Firebase credentials not configured. Running in development mode without Firestore.
```

## Step 6: Test Database Operations

You can test if Firestore is working by:

1. **Create a test trip** via API
2. **Check Firestore Console** to see if data appears
3. **Try trip search** to verify read operations

## Firestore Database Setup

Make sure your Firestore database is set up:

1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Select **dailydrive-892d3** project
3. Go to **Firestore Database**
4. Click **Create database**
5. Choose **Start in test mode** (for development)
6. Select a location (preferably close to your users)

## Security Rules (Development)

For development, use these Firestore rules:
```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    match /{document=**} {
      allow read, write: if true;
    }
  }
}
```

⚠️ **Note**: These rules allow all access. Update for production!

## Troubleshooting

### Common Issues:

1. **"Invalid PKCS#8 data"**
   - Make sure private key includes `\n` characters
   - Ensure proper escaping in environment variables

2. **"FirebaseApp doesn't exist"**
   - Check all environment variables are set
   - Restart the application after setting variables

3. **"Permission denied"**
   - Verify Firestore database is created
   - Check security rules allow access
   - Ensure service account has proper permissions

### Test Firebase Connection:
```bash
# Test geocoding (should work)
curl "http://localhost:8080/api/trips/geocode?address=New York"

# Test trip creation (will work once Firebase is configured)
curl -X POST "http://localhost:8080/api/trips" \
  -H "Content-Type: application/json" \
  -d '{"origin":"New York","destination":"Boston","departureDateTime":"2024-06-05T10:00:00"}'
```
