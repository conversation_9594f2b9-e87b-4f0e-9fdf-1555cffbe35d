# DailyRide Backend API Test Script
# PowerShell script to test all backend endpoints

Write-Host "🚗 DailyRide Backend API Testing Suite" -ForegroundColor Green
Write-Host "=======================================" -ForegroundColor Green

$baseUrl = "http://localhost:8090/api"
$testResults = @()

function Test-Endpoint {
    param(
        [string]$Method,
        [string]$Url,
        [string]$Description,
        [hashtable]$Body = $null,
        [hashtable]$Headers = @{}
    )
    
    Write-Host "`n🧪 Testing: $Description" -ForegroundColor Yellow
    Write-Host "   $Method $Url" -ForegroundColor Gray
    
    try {
        $params = @{
            Uri = $Url
            Method = $Method
            Headers = $Headers
            TimeoutSec = 10
        }
        
        if ($Body) {
            $params.Body = ($Body | ConvertTo-Json)
            $params.ContentType = "application/json"
        }
        
        $response = Invoke-RestMethod @params
        Write-Host "   ✅ SUCCESS: $($response.GetType().Name)" -ForegroundColor Green
        
        $global:testResults += @{
            Test = $Description
            Status = "PASS"
            Method = $Method
            Url = $Url
            Response = $response
        }
        
        return $response
    }
    catch {
        Write-Host "   ❌ FAILED: $($_.Exception.Message)" -ForegroundColor Red
        
        $global:testResults += @{
            Test = $Description
            Status = "FAIL"
            Method = $Method
            Url = $Url
            Error = $_.Exception.Message
        }
        
        return $null
    }
}

# Test 1: Health Check
Write-Host "`n📊 HEALTH CHECK TESTS" -ForegroundColor Cyan
Test-Endpoint -Method "GET" -Url "$baseUrl/health" -Description "Basic Health Check"
Test-Endpoint -Method "GET" -Url "$baseUrl/health/detailed" -Description "Detailed Health Check"
Test-Endpoint -Method "GET" -Url "$baseUrl/health/stats" -Description "System Statistics"
Test-Endpoint -Method "GET" -Url "$baseUrl/health/config" -Description "Configuration Status"

# Test 2: User Management
Write-Host "`n👤 USER MANAGEMENT TESTS" -ForegroundColor Cyan

# Create test user
$testUser = @{
    firstName = "Test"
    lastName = "User"
    email = "<EMAIL>"
    phone = "+**********"
    userType = "BOTH"
}

Test-Endpoint -Method "POST" -Url "$baseUrl/users/test-user" -Description "Create Test User"

# Test login
Test-Endpoint -Method "POST" -Url "$baseUrl/users/login?email=<EMAIL>&password=TestPassword123!" -Description "User Login"

# Test 3: Trip Management
Write-Host "`n🚗 TRIP MANAGEMENT TESTS" -ForegroundColor Cyan

# Search trips
Test-Endpoint -Method "GET" -Url "$baseUrl/trips/search?origin=Rabat&destination=Casablanca" -Description "Search Trips"

# Test geocoding
Test-Endpoint -Method "GET" -Url "$baseUrl/trips/geocode?address=Casablanca" -Description "Geocode Address"

# Test directions
Test-Endpoint -Method "GET" -Url "$baseUrl/trips/directions?origin=Rabat&destination=Casablanca" -Description "Get Directions"

# Test Firebase connectivity
Test-Endpoint -Method "GET" -Url "$baseUrl/trips/test-firebase" -Description "Firebase Connectivity"

# Test 4: Integration Tests
Write-Host "`n🔗 INTEGRATION TESTS" -ForegroundColor Cyan

$integrationData = @{
    testType = "automated"
    timestamp = (Get-Date).ToString("yyyy-MM-dd HH:mm:ss")
    source = "PowerShell Test Script"
}

Test-Endpoint -Method "POST" -Url "$baseUrl/health/integration-test" -Description "Integration Test" -Body $integrationData

# Test Results Summary
Write-Host "`n📋 TEST RESULTS SUMMARY" -ForegroundColor Magenta
Write-Host "========================" -ForegroundColor Magenta

$passCount = ($testResults | Where-Object { $_.Status -eq "PASS" }).Count
$failCount = ($testResults | Where-Object { $_.Status -eq "FAIL" }).Count
$totalCount = $testResults.Count

Write-Host "Total Tests: $totalCount" -ForegroundColor White
Write-Host "Passed: $passCount" -ForegroundColor Green
Write-Host "Failed: $failCount" -ForegroundColor Red
Write-Host "Success Rate: $([math]::Round(($passCount / $totalCount) * 100, 2))%" -ForegroundColor Yellow

if ($failCount -gt 0) {
    Write-Host "`n❌ FAILED TESTS:" -ForegroundColor Red
    $testResults | Where-Object { $_.Status -eq "FAIL" } | ForEach-Object {
        Write-Host "   - $($_.Test): $($_.Error)" -ForegroundColor Red
    }
}

Write-Host "`n✅ PASSED TESTS:" -ForegroundColor Green
$testResults | Where-Object { $_.Status -eq "PASS" } | ForEach-Object {
    Write-Host "   - $($_.Test)" -ForegroundColor Green
}

# Save results to file
$testResults | ConvertTo-Json -Depth 3 | Out-File "test-results-$(Get-Date -Format 'yyyyMMdd-HHmmss').json"
Write-Host "`n💾 Test results saved to test-results-$(Get-Date -Format 'yyyyMMdd-HHmmss').json" -ForegroundColor Blue

Write-Host "`n🎯 Backend API Testing Complete!" -ForegroundColor Green
