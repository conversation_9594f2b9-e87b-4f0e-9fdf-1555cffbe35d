@echo off
echo 🚗 Starting DailyRide Application (Expo SDK 53)...
echo.

echo 🔧 Applying database fixes...
node fix-database-issues.js
echo.

echo 📱 Starting Frontend (React Native + Expo SDK 53)...
cd mobile
start "DailyRide Frontend" cmd /k "npx expo start --web"
cd ..

echo ⏳ Waiting 10 seconds for frontend to initialize...
timeout /t 10 /nobreak > nul

echo 🖥️ Starting Backend (Spring Boot)...
cd backend
start "DailyRide Backend" cmd /k "mvn spring-boot:run"
cd ..

echo.
echo ✅ Both services are starting...
echo 📱 Frontend: http://localhost:19006 (Expo SDK 53)
echo 🖥️ Backend:  http://localhost:8090/api
echo 📚 API Docs: http://localhost:8090/api/swagger-ui.html
echo.
echo 🔐 Demo Credentials:
echo 👨‍💼 Admin: <EMAIL> / admin
echo 🚗 Driver: <EMAIL> / driver123
echo 👤 Passenger: <EMAIL> / passenger123
echo.
echo 🎯 Features Available:
echo   ✅ inDrive-style bidding system
echo   ✅ Real-time trip management
echo   ✅ Google Maps integration
echo   ✅ Payment processing
echo   ✅ Admin dashboard
echo.
echo 📱 Mobile Testing Options:
echo   🌐 Web Browser: http://localhost:19006
echo   📱 Expo Go: Scan QR code from terminal
echo   📱 iOS Simulator: Press 'i' in Expo terminal
echo   🤖 Android Emulator: Press 'a' in Expo terminal
echo.
echo 🎉 Your DailyRide app is now running with Expo SDK 53!
pause
