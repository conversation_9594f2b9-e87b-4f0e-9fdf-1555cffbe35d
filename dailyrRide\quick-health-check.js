#!/usr/bin/env node

/**
 * 🏥 DailyRide - Quick Health Check
 * Verifies system is ready for Expo SDK 53
 */

const fs = require('fs');
const path = require('path');

console.log('🏥 DailyRide - Quick Health Check for Expo SDK 53');
console.log('=' .repeat(60));

let allGood = true;

function check(name, condition, details = '') {
  const status = condition ? '✅' : '❌';
  console.log(`${status} ${name}`);
  if (details) console.log(`   ${details}`);
  if (!condition) allGood = false;
  return condition;
}

// 1. Check Expo SDK 53
try {
  const pkg = JSON.parse(fs.readFileSync('./mobile/package.json', 'utf8'));
  check('Expo SDK 53', pkg.dependencies.expo?.includes('53.'), `Version: ${pkg.dependencies.expo}`);
  check('React 19', pkg.dependencies.react === '19.0.0', `Version: ${pkg.dependencies.react}`);
  check('React Native 0.79', pkg.dependencies['react-native'] === '0.79.2', `Version: ${pkg.dependencies['react-native']}`);
} catch (e) {
  check('Package.json', false, 'Could not read mobile/package.json');
}

// 2. Check App Configuration
try {
  const app = JSON.parse(fs.readFileSync('./mobile/app.json', 'utf8'));
  check('App Configuration', !!app.expo?.name, `App: ${app.expo?.name || 'Unknown'}`);
  check('Location Permissions', 
    app.expo?.android?.permissions?.includes('ACCESS_FINE_LOCATION'), 
    'Location permissions configured');
} catch (e) {
  check('App.json', false, 'Could not read mobile/app.json');
}

// 3. Check TypeScript
try {
  const ts = JSON.parse(fs.readFileSync('./mobile/tsconfig.json', 'utf8'));
  check('TypeScript Config', !!ts.compilerOptions?.baseUrl, 'TypeScript properly configured');
  check('Path Aliases', !!ts.compilerOptions?.paths?.['@/*'], 'Path aliases configured');
} catch (e) {
  check('TypeScript', false, 'Could not read mobile/tsconfig.json');
}

// 4. Check Backend
try {
  const pom = fs.readFileSync('./backend/pom.xml', 'utf8');
  check('Backend POM', pom.includes('<name>DailyRide Backend</name>'), 'Maven configuration valid');
  check('Spring Boot', pom.includes('spring-boot-starter-web'), 'Spring Boot configured');
} catch (e) {
  check('Backend Configuration', false, 'Could not read backend/pom.xml');
}

// 5. Check File Structure
const requiredFiles = [
  './mobile/App.tsx',
  './mobile/src/store/index.ts',
  './mobile/src/navigation/AppNavigator.tsx',
  './mobile/src/services/database.ts',
  './backend/src/main/java/com/dailyride/DailyRideApplication.java'
];

let filesExist = 0;
requiredFiles.forEach(file => {
  if (fs.existsSync(file)) filesExist++;
});

check('File Structure', filesExist === requiredFiles.length, 
  `${filesExist}/${requiredFiles.length} required files present`);

// 6. Check Dependencies
try {
  const pkg = JSON.parse(fs.readFileSync('./mobile/package.json', 'utf8'));
  const criticalDeps = [
    '@react-navigation/native',
    '@react-navigation/bottom-tabs',
    'react-native-maps',
    'react-native-reanimated',
    '@reduxjs/toolkit'
  ];
  
  const missingDeps = criticalDeps.filter(dep => !pkg.dependencies[dep]);
  check('Critical Dependencies', missingDeps.length === 0, 
    missingDeps.length > 0 ? `Missing: ${missingDeps.join(', ')}` : 'All dependencies present');
} catch (e) {
  check('Dependencies Check', false, 'Could not verify dependencies');
}

console.log('\n' + '='.repeat(60));

if (allGood) {
  console.log('🎉 PERFECT! Your DailyRide system is fully optimized for Expo SDK 53!');
  console.log('✅ Ready for development and production deployment');
  console.log('🚀 You can now run: npm start or expo start');
} else {
  console.log('⚠️  Some issues detected. Please review the items marked with ❌');
  console.log('📋 Check the SYSTEM_ANALYSIS_REPORT.md for detailed recommendations');
}

console.log('\n📊 System Status: ' + (allGood ? 'EXCELLENT' : 'NEEDS ATTENTION'));
console.log('🔗 For detailed analysis, see: SYSTEM_ANALYSIS_REPORT.md');
console.log('=' .repeat(60));
