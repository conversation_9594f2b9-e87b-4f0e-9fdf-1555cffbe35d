package com.dailyride.exception;

/**
 * Base exception class for all DailyRide application exceptions.
 */
public class DailyRideException extends RuntimeException {
    
    private final String errorCode;
    
    public DailyRideException(String message) {
        super(message);
        this.errorCode = "GENERAL_ERROR";
    }
    
    public DailyRideException(String message, String errorCode) {
        super(message);
        this.errorCode = errorCode;
    }
    
    public DailyRideException(String message, Throwable cause) {
        super(message, cause);
        this.errorCode = "GENERAL_ERROR";
    }
    
    public DailyRideException(String message, String errorCode, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
    }
    
    public String getErrorCode() {
        return errorCode;
    }
}
