{"compilerOptions": {"target": "esnext", "lib": ["dom", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": false, "noImplicitAny": false, "strictNullChecks": false, "strictFunctionTypes": false, "strictBindCallApply": false, "strictPropertyInitialization": false, "noImplicitReturns": false, "noImplicitThis": false, "alwaysStrict": false, "noUnusedLocals": false, "noUnusedParameters": false, "exactOptionalPropertyTypes": false, "noImplicitOverride": false, "noPropertyAccessFromIndexSignature": false, "noUncheckedIndexedAccess": false, "forceConsistentCasingInFileNames": true, "noEmit": true, "esModuleInterop": true, "jsx": "react-native", "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "noFallthroughCasesInSwitch": true, "allowSyntheticDefaultImports": true, "baseUrl": ".", "paths": {"@/*": ["src/*"], "@/components/*": ["src/components/*"], "@/screens/*": ["src/screens/*"], "@/services/*": ["src/services/*"], "@/store/*": ["src/store/*"], "@/types/*": ["src/types/*"], "@/utils/*": ["src/utils/*"], "@/constants/*": ["src/constants/*"], "@/navigation/*": ["src/navigation/*"], "@/contexts/*": ["src/contexts/*"]}}, "include": ["**/*.ts", "**/*.tsx", "App.tsx"], "exclude": ["node_modules"], "extends": "expo/tsconfig.base"}