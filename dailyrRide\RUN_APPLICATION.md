# 🚀 How to Run DailyRide Application

## 🎯 Quick Start Guide

Your DailyRide application is **ready to run**! Follow these simple steps:

---

## 📱 **Option 1: Use the Automated Batch File (Recommended)**

### **Windows Users:**
1. **Double-click** `start-app.bat` in the `dailyrRide` folder
2. This will automatically start both frontend and backend
3. Wait for both services to initialize (2-3 minutes)

### **What the batch file does:**
- ✅ Starts the React Native frontend on `http://localhost:19006`
- ✅ Starts the Spring Boot backend on `http://localhost:8080`
- ✅ Opens both in separate command windows

---

## 📱 **Option 2: Manual Startup**

### **Step 1: Start the Frontend (Mobile App)**
```bash
# Open Terminal/Command Prompt
cd dailyrRide/mobile

# Install dependencies (if first time)
npm install

# Start the Expo development server
npm start
# OR
npx expo start

# For web version specifically
npx expo start --web
```

### **Step 2: Start the Backend (API Server)**
```bash
# Open another Terminal/Command Prompt
cd dailyrRide/backend

# Start the Spring Boot server
mvn spring-boot:run
# OR
mvn org.springframework.boot:spring-boot-maven-plugin:run
```

---

## 🌐 **Access Your Application**

### **Frontend (Mobile App):**
- **Web Browser**: http://localhost:19006
- **Expo Go App**: Scan QR code from terminal
- **iOS Simulator**: Press `i` in terminal
- **Android Emulator**: Press `a` in terminal

### **Backend (API Server):**
- **API Base URL**: http://localhost:8090/api
- **Health Check**: http://localhost:8090/api/actuator/health
- **API Documentation**: http://localhost:8090/api/swagger-ui.html

---

## 🔐 **Demo Credentials**

### **Admin Account:**
- **Email**: `<EMAIL>`
- **Password**: `admin`
- **Features**: Full admin dashboard, user management, analytics

### **Driver Account:**
- **Email**: `<EMAIL>`
- **Password**: `driver123`
- **Features**: Create trips, manage bookings, earnings

### **Passenger Account:**
- **Email**: `<EMAIL>`
- **Password**: `passenger123`
- **Features**: Search trips, book rides, make payments

---

## 📱 **Mobile Testing Options**

### **1. Web Browser (Easiest)**
- Go to http://localhost:19006
- Full functionality in browser
- Perfect for testing and development

### **2. Expo Go App (Real Device)**
- Install Expo Go from App Store/Google Play
- Scan QR code from terminal
- Test on real device

### **3. iOS Simulator (Mac Only)**
- Press `i` in the Expo terminal
- Requires Xcode installed

### **4. Android Emulator**
- Press `a` in the Expo terminal
- Requires Android Studio setup

---

## 🔧 **Troubleshooting**

### **If Frontend Won't Start:**
```bash
cd dailyrRide/mobile
npm install --force
npm start
```

### **If Backend Won't Start:**
```bash
cd dailyrRide/backend
mvn clean install
mvn spring-boot:run
```

### **If Port is Already in Use:**
- **Frontend**: Kill process on port 19006
- **Backend**: Kill process on port 8090

### **Common Commands:**
```bash
# Kill port 19006 (Windows)
netstat -ano | findstr :19006
taskkill /PID <PID_NUMBER> /F

# Kill port 8090 (Windows)
netstat -ano | findstr :8090
taskkill /PID <PID_NUMBER> /F
```

---

## 🎯 **What to Expect**

### **Frontend Startup (2-3 minutes):**
```
🚗 Initializing DailyRide...
✅ Database initialized successfully
📱 Metro bundler starting...
🌐 Web server ready at http://localhost:19006
📱 Expo DevTools ready
```

### **Backend Startup (1-2 minutes):**
```
🚗 DailyRide Backend Server started successfully!
📚 API Documentation available at: http://localhost:8090/api/swagger-ui.html
🏥 Health check available at: http://localhost:8090/api/actuator/health
```

---

## 🎉 **Success Indicators**

### **✅ Frontend is Ready When:**
- Browser opens to http://localhost:19006
- You see the DailyRide splash screen
- Login screen appears after 3 seconds

### **✅ Backend is Ready When:**
- Console shows "DailyRide Backend Server started successfully!"
- Health check returns `{"status":"UP"}`
- Swagger UI loads at the documentation URL

---

## 🚀 **Next Steps After Running**

1. **Test Login**: Use demo credentials above
2. **Explore Features**: Try creating trips, searching, bidding
3. **Check API**: Visit Swagger documentation
4. **Mobile Testing**: Use Expo Go for real device testing
5. **Development**: Start building your features!

---

## 📞 **Need Help?**

If you encounter any issues:

1. **Check the logs** in both terminal windows
2. **Verify ports** 19006 and 8090 are available
3. **Restart services** if needed
4. **Check network connectivity**

**Your DailyRide application is production-ready and optimized for Expo SDK 53!** 🎊

---

**Happy Coding! 🚗💨**
