package com.dailyride;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableAsync;

/**
 * Main Spring Boot application class for DailyRide carpooling platform.
 * 
 * This application provides:
 * - User authentication and management
 * - Trip publishing and searching
 * - Booking system
 * - Real-time chat functionality
 * - Rating and review system
 * - Google Maps integration
 * - Firebase push notifications
 */
@SpringBootApplication
@EnableAsync
public class DailyRideApplication {

    public static void main(String[] args) {
        SpringApplication.run(DailyRideApplication.class, args);
        System.out.println("🚗 DailyRide Backend Server started successfully!");
        System.out.println("📚 API Documentation available at: http://localhost:8090/api/swagger-ui.html");
        System.out.println("🏥 Health check available at: http://localhost:8090/api/actuator/health");
    }
}
