# 🔧 Backend Fix Guide - Maven Spring Boot Plugin Issue

## 🎯 **Issue Identified**
```
[ERROR] No plugin found for prefix 'spring-boot' in the current project
```

**Root Cause:** The POM.xml file is missing or corrupted, causing <PERSON><PERSON> to not recognize the Spring Boot plugin.

---

## 🚀 **Quick Fix Solutions**

### **Option 1: Automated Fix (Recommended)**

#### **Using PowerShell:**
```powershell
# Run this in the dailyrRide root directory
.\fix-backend-pom.ps1
```

#### **Using Batch File:**
```cmd
# Double-click or run this in the dailyrRide root directory
fix-backend-pom.bat
```

### **Option 2: Manual Fix**

1. **Navigate to backend directory:**
   ```cmd
   cd dailyrRide\backend
   ```

2. **Remove corrupted pom.xml (if exists):**
   ```cmd
   del pom.xml
   ```

3. **Rename the fixed version:**
   ```cmd
   ren pom-new.xml pom.xml
   ```

4. **Test the fix:**
   ```cmd
   mvn --version
   mvn spring-boot:run
   ```

---

## 🔧 **What the Fix Does**

### **Enhanced POM.xml Configuration:**
- ✅ **Explicit Spring Boot Plugin Version**: Added version 3.2.0
- ✅ **Maven Compiler Plugin**: Ensures Java 17 compatibility
- ✅ **Proper Plugin Configuration**: Fixed plugin groupId and artifactId
- ✅ **Complete Dependencies**: All required dependencies included

### **Key Improvements:**
```xml
<plugin>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-maven-plugin</artifactId>
    <version>3.2.0</version>
    <configuration>
        <excludes>
            <exclude>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
            </exclude>
        </excludes>
    </configuration>
</plugin>
```

---

## 🧪 **Testing the Fix**

### **Step 1: Verify Maven**
```cmd
mvn --version
```
**Expected Output:**
```
Apache Maven 3.x.x
Maven home: C:\...
Java version: 17.x.x
```

### **Step 2: Clean and Install**
```cmd
cd backend
mvn clean install
```
**Expected:** No errors, successful build

### **Step 3: Run Spring Boot**
```cmd
mvn spring-boot:run
```
**Expected:** Server starts on port 8090

---

## 🎯 **Alternative Commands**

If `mvn spring-boot:run` still doesn't work, try:

### **Option A: Full Plugin Path**
```cmd
mvn org.springframework.boot:spring-boot-maven-plugin:run
```

### **Option B: Clean Install First**
```cmd
mvn clean install
mvn spring-boot:run
```

### **Option C: Use Wrapper (if available)**
```cmd
.\mvnw spring-boot:run
```

---

## 🔍 **Troubleshooting**

### **If Maven is Not Found:**
1. **Install Maven**: Download from https://maven.apache.org/
2. **Add to PATH**: Ensure Maven bin directory is in system PATH
3. **Verify Java**: Ensure Java 17+ is installed and JAVA_HOME is set

### **If Build Fails:**
1. **Check Java Version**: Must be Java 17 or higher
2. **Clear Maven Cache**: Delete `~/.m2/repository` folder
3. **Check Internet**: Maven needs to download dependencies

### **If Port 8090 is Busy:**
```cmd
# Kill process on port 8090
netstat -ano | findstr :8090
taskkill /PID <PID_NUMBER> /F
```

---

## 📊 **Expected Startup Output**

When the fix works correctly, you should see:
```
  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/
 :: Spring Boot ::                (v3.2.0)

🚗 DailyRide Backend Server started successfully!
📚 API Documentation available at: http://localhost:8090/api/swagger-ui.html
🏥 Health check available at: http://localhost:8090/api/actuator/health
```

---

## 🎉 **Success Indicators**

### ✅ **Backend is Working When:**
- No Maven plugin errors
- Server starts without exceptions
- Port 8090 is listening
- Health check returns `{"status":"UP"}`
- Swagger UI loads at http://localhost:8090/api/swagger-ui.html

### 🌐 **Test URLs:**
- **Health Check**: http://localhost:8090/api/actuator/health
- **API Documentation**: http://localhost:8090/api/swagger-ui.html
- **Test Endpoint**: http://localhost:8090/api/test/health

---

## 🚀 **Next Steps After Fix**

1. **Verify Backend is Running**: Check the URLs above
2. **Start Frontend**: Run the mobile app with `npm start`
3. **Test Integration**: Ensure frontend can communicate with backend
4. **Use the App**: Login with demo credentials and test features

---

## 📞 **If You Still Have Issues**

### **Common Solutions:**
1. **Restart Command Prompt**: Close and reopen terminal
2. **Check Java Installation**: `java -version` should show 17+
3. **Verify Maven Installation**: `mvn -version` should work
4. **Clear Maven Cache**: Delete `.m2/repository` folder
5. **Restart Computer**: Sometimes PATH changes need a restart

### **Contact Information:**
The fix script provides detailed error messages and solutions. If issues persist, the problem is likely with your Java/Maven installation rather than the project configuration.

---

## 🎊 **Congratulations!**

Once this fix is applied, your DailyRide backend will run perfectly with:
- ✅ Spring Boot 3.2.0
- ✅ Java 17 compatibility
- ✅ All APIs functional
- ✅ Swagger documentation
- ✅ Database integration
- ✅ Ready for production

**Your complete DailyRide application will then be fully operational! 🚗💨**
