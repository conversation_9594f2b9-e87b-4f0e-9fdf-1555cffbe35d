package com.dailyride.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.*;
import jakarta.validation.constraints.*;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;

import java.util.*;

/**
 * User entity representing both passengers and drivers in the DailyRide system.
 * Implements UserDetails for Spring Security integration.
 * Stored in SQLite 'users' table.
 */
@Entity
@Table(name = "users")
public class User implements UserDetails {

    @Id
    private String id;

    @Column(name = "createdAt")
    private String createdAt;

    @Column(name = "updatedAt")
    private String updatedAt;

    @Column(unique = true, nullable = false)
    @Email(message = "Email should be valid")
    @NotBlank(message = "Email is required")
    private String email;

    @JsonIgnore
    @Column(nullable = false)
    @Size(min = 6, message = "Password must be at least 6 characters")
    private String password;

    @Column(nullable = false)
    @NotBlank(message = "First name is required")
    @Size(min = 2, max = 50, message = "First name must be between 2 and 50 characters")
    private String firstName;

    @Column(nullable = false)
    @NotBlank(message = "Last name is required")
    @Size(min = 2, max = 50, message = "Last name must be between 2 and 50 characters")
    private String lastName;

    @Column
    private String phone;

    @Column(nullable = false)
    @Enumerated(EnumType.STRING)
    private UserType userType = UserType.PASSENGER;

    @Column
    private Double averageRating = 0.0;

    @Column
    private Integer totalRatings = 0;

    @Column
    private Boolean isVerified = false;

    @Column
    private String gender;

    @Column
    private String bio;

    @Column
    private String profilePhoto;

    // Enums
    public enum UserType {
        PASSENGER,  // Clients who take rides and book them
        DRIVER,     // Drivers who offer rides and can be booked
        ADMIN       // Administrators who manage users and the app
    }

    // Constructors
    public User() {}

    public User(String email, String password, String firstName, String lastName, 
                String phone, UserType userType) {
        this.id = "user-" + System.currentTimeMillis() + "-" + Math.random();
        this.email = email;
        this.password = password;
        this.firstName = firstName;
        this.lastName = lastName;
        this.phone = phone;
        this.userType = userType;
        this.createdAt = java.time.LocalDateTime.now().toString();
        this.updatedAt = java.time.LocalDateTime.now().toString();
    }

    // UserDetails implementation
    @Override
    @JsonIgnore
    public Collection<? extends GrantedAuthority> getAuthorities() {
        List<GrantedAuthority> authorities = new ArrayList<>();
        authorities.add(new SimpleGrantedAuthority("ROLE_USER"));

        if (userType == UserType.DRIVER) {
            authorities.add(new SimpleGrantedAuthority("ROLE_DRIVER"));
        }

        if (userType == UserType.PASSENGER) {
            authorities.add(new SimpleGrantedAuthority("ROLE_PASSENGER"));
        }

        if (userType == UserType.ADMIN) {
            authorities.add(new SimpleGrantedAuthority("ROLE_ADMIN"));
            authorities.add(new SimpleGrantedAuthority("ROLE_DRIVER"));
            authorities.add(new SimpleGrantedAuthority("ROLE_PASSENGER"));
        }

        return authorities;
    }

    @Override
    @JsonIgnore
    public String getUsername() {
        return email;
    }

    @Override
    @JsonIgnore
    public boolean isAccountNonExpired() {
        return true;
    }

    @Override
    @JsonIgnore
    public boolean isAccountNonLocked() {
        return true;
    }

    @Override
    @JsonIgnore
    public boolean isCredentialsNonExpired() {
        return true;
    }

    @Override
    @JsonIgnore
    public boolean isEnabled() {
        return isVerified != null ? isVerified : false;
    }

    // Business methods
    public String getFullName() {
        return firstName + " " + lastName;
    }

    public void updateRating(Double newRating) {
        if (totalRatings == null) totalRatings = 0;
        if (averageRating == null) averageRating = 0.0;
        
        double totalScore = averageRating * totalRatings + newRating;
        this.totalRatings += 1;
        this.averageRating = totalScore / totalRatings;
    }

    public boolean canDrive() {
        return userType == UserType.DRIVER;
    }

    public boolean canBook() {
        return userType == UserType.PASSENGER || userType == UserType.ADMIN;
    }

    public boolean isAdmin() {
        return userType == UserType.ADMIN;
    }

    // Getters and Setters
    public String getId() { return id; }
    public void setId(String id) { this.id = id; }

    public String getCreatedAt() { return createdAt; }
    public void setCreatedAt(String createdAt) { this.createdAt = createdAt; }

    public String getUpdatedAt() { return updatedAt; }
    public void setUpdatedAt(String updatedAt) { this.updatedAt = updatedAt; }

    public String getEmail() { return email; }
    public void setEmail(String email) { this.email = email; }

    public String getPassword() { return password; }
    public void setPassword(String password) { this.password = password; }

    public String getFirstName() { return firstName; }
    public void setFirstName(String firstName) { this.firstName = firstName; }

    public String getLastName() { return lastName; }
    public void setLastName(String lastName) { this.lastName = lastName; }

    public String getPhone() { return phone; }
    public void setPhone(String phone) { this.phone = phone; }

    public UserType getUserType() { return userType; }
    public void setUserType(UserType userType) { this.userType = userType; }

    public Double getAverageRating() { return averageRating; }
    public void setAverageRating(Double averageRating) { this.averageRating = averageRating; }

    public Integer getTotalRatings() { return totalRatings; }
    public void setTotalRatings(Integer totalRatings) { this.totalRatings = totalRatings; }

    public Boolean getIsVerified() { return isVerified; }
    public void setIsVerified(Boolean isVerified) { this.isVerified = isVerified; }

    public String getGender() { return gender; }
    public void setGender(String gender) { this.gender = gender; }

    public String getBio() { return bio; }
    public void setBio(String bio) { this.bio = bio; }

    public String getProfilePhoto() { return profilePhoto; }
    public void setProfilePhoto(String profilePhoto) { this.profilePhoto = profilePhoto; }

    // Convenience methods for compatibility
    public String getAvatarUrl() { return profilePhoto; }
    public void setAvatarUrl(String avatarUrl) { this.profilePhoto = avatarUrl; }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof User)) return false;
        User user = (User) o;
        return Objects.equals(id, user.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public String toString() {
        return "User{" +
                "id='" + id + '\'' +
                ", email='" + email + '\'' +
                ", firstName='" + firstName + '\'' +
                ", lastName='" + lastName + '\'' +
                ", userType=" + userType +
                ", isVerified=" + isVerified +
                '}';
    }
}
