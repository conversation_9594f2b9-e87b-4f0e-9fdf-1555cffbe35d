package com.dailyride.repository;

import com.dailyride.model.Trip;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * JPA Repository for Trip entity operations with SQLite database.
 */
@Repository
public interface TripJpaRepository extends JpaRepository<Trip, String> {

    /**
     * Find all active trips.
     */
    List<Trip> findByStatusAndAvailableSeatsGreaterThan(Trip.TripStatus status, Integer seats);

    /**
     * Find trips by driver.
     */
    List<Trip> findByDriverId(String driverId);

    /**
     * Find trips by driver and status.
     */
    List<Trip> findByDriverIdAndStatus(String driverId, Trip.TripStatus status);

    /**
     * Find active trips by driver.
     */
    @Query("SELECT t FROM Trip t WHERE t.driverId = :driverId AND t.status = 'ACTIVE'")
    List<Trip> findActiveTripsByDriver(@Param("driverId") String driverId);

    /**
     * Find completed trips by driver.
     */
    @Query("SELECT t FROM Trip t WHERE t.driverId = :driverId AND t.status = 'COMPLETED' ORDER BY t.departureDateTime DESC")
    List<Trip> findCompletedTripsByDriver(@Param("driverId") String driverId);

    /**
     * Find trips by price range.
     */
    @Query("SELECT t FROM Trip t WHERE t.status = 'ACTIVE' AND t.availableSeats > 0 AND t.pricePerSeat BETWEEN :minPrice AND :maxPrice ORDER BY t.pricePerSeat ASC")
    List<Trip> findByPriceRange(@Param("minPrice") Double minPrice, @Param("maxPrice") Double maxPrice);

    /**
     * Find trips with available seats.
     */
    @Query("SELECT t FROM Trip t WHERE t.status = 'ACTIVE' AND t.availableSeats >= :minSeats ORDER BY t.departureDateTime ASC")
    List<Trip> findTripsWithAvailableSeats(@Param("minSeats") Integer minSeats);

    /**
     * Find trips by origin and destination.
     */
    @Query("SELECT t FROM Trip t WHERE t.status = 'ACTIVE' AND t.originAddress LIKE %:origin% AND t.destinationAddress LIKE %:destination% ORDER BY t.departureDateTime ASC")
    List<Trip> findByOriginAndDestination(@Param("origin") String origin, @Param("destination") String destination);

    /**
     * Find trips by status.
     */
    List<Trip> findByStatusOrderByDepartureDateTimeAsc(Trip.TripStatus status);

    /**
     * Count active trips by driver.
     */
    @Query("SELECT COUNT(t) FROM Trip t WHERE t.driverId = :driverId AND t.status = 'ACTIVE'")
    Long countActiveTripsByDriver(@Param("driverId") String driverId);

    /**
     * Count trips by driver.
     */
    Long countByDriverId(String driverId);

    /**
     * Count trips by driver and status.
     */
    Long countByDriverIdAndStatus(String driverId, Trip.TripStatus status);

    /**
     * Find recent trips.
     */
    @Query("SELECT t FROM Trip t WHERE t.status = 'ACTIVE' AND t.availableSeats > 0 ORDER BY t.createdAt DESC")
    List<Trip> findRecentTrips();

    /**
     * Find popular routes.
     */
    @Query("SELECT t FROM Trip t WHERE t.status = 'COMPLETED' ORDER BY t.createdAt DESC")
    List<Trip> findPopularRoutes();

    /**
     * Find all active trips with available seats.
     */
    @Query("SELECT t FROM Trip t WHERE t.status = 'ACTIVE' AND t.availableSeats > 0 ORDER BY t.departureDateTime ASC")
    List<Trip> findAllActiveTrips();

    /**
     * Search trips by text in origin or destination.
     */
    @Query("SELECT t FROM Trip t WHERE t.status = 'ACTIVE' AND t.availableSeats > 0 AND (t.originAddress LIKE %:searchText% OR t.destinationAddress LIKE %:searchText%) ORDER BY t.departureDateTime ASC")
    List<Trip> searchTripsByText(@Param("searchText") String searchText);

    /**
     * Find trips by departure date range.
     */
    @Query("SELECT t FROM Trip t WHERE t.status = 'ACTIVE' AND t.availableSeats > 0 AND t.departureDateTime BETWEEN :startDate AND :endDate ORDER BY t.departureDateTime ASC")
    List<Trip> findTripsByDateRange(@Param("startDate") String startDate, @Param("endDate") String endDate);

    /**
     * Count trips by status.
     */
    Long countByStatus(Trip.TripStatus status);
}
