{"version": 3, "names": ["_helperWrapFunction", "require", "_helperAnnotateAsPure", "_core", "_traverse", "callExpression", "cloneNode", "isIdentifier", "isThisExpression", "yieldExpression", "t", "await<PERSON><PERSON>tor", "visitors", "environmentVisitor", "ArrowFunctionExpression", "path", "skip", "AwaitExpression", "wrapAwait", "argument", "get", "replaceWith", "node", "_default", "helpers", "noNewArrows", "ignoreFunctionLength", "traverse", "isIIFE", "checkIsIIFE", "async", "generator", "wrapFunction", "wrapAsync", "isProperty", "isObjectMethod", "isClassMethod", "parentPath", "isObjectProperty", "isClassProperty", "isExpression", "annotateAsPure", "isCallExpression", "callee", "isMemberExpression", "property", "name", "bindCall", "arguments", "length"], "sources": ["../src/index.ts"], "sourcesContent": ["import type { NodePath } from \"@babel/core\";\nimport wrapFunction from \"@babel/helper-wrap-function\";\nimport annotateAsPure from \"@babel/helper-annotate-as-pure\";\nimport { types as t } from \"@babel/core\";\nimport { visitors } from \"@babel/traverse\";\nconst {\n  callExpression,\n  cloneNode,\n  isIdentifier,\n  isThisExpression,\n  yieldExpression,\n} = t;\n\nconst awaitVisitor = visitors.environmentVisitor<{ wrapAwait: t.Expression }>({\n  ArrowFunctionExpression(path) {\n    path.skip();\n  },\n\n  AwaitExpression(path, { wrapAwait }) {\n    const argument = path.get(\"argument\");\n\n    path.replaceWith(\n      yieldExpression(\n        wrapAwait\n          ? callExpression(cloneNode(wrapAwait), [argument.node])\n          : argument.node,\n      ),\n    );\n  },\n});\n\nexport default function (\n  path: NodePath<t.Function>,\n  helpers: {\n    wrapAsync: t.Expression;\n    wrapAwait?: t.Expression;\n  },\n  noNewArrows?: boolean,\n  ignoreFunctionLength?: boolean,\n) {\n  path.traverse(awaitVisitor, {\n    wrapAwait: helpers.wrapAwait,\n  });\n\n  const isIIFE = checkIsIIFE(path);\n\n  path.node.async = false;\n  path.node.generator = true;\n\n  wrapFunction(\n    path,\n    cloneNode(helpers.wrapAsync),\n    noNewArrows,\n    ignoreFunctionLength,\n  );\n\n  const isProperty =\n    path.isObjectMethod() ||\n    path.isClassMethod() ||\n    path.parentPath.isObjectProperty() ||\n    path.parentPath.isClassProperty();\n\n  if (!isProperty && !isIIFE && path.isExpression()) {\n    annotateAsPure(path);\n  }\n\n  function checkIsIIFE(path: NodePath) {\n    if (path.parentPath.isCallExpression({ callee: path.node })) {\n      return true;\n    }\n\n    // try to catch calls to Function#bind, as emitted by arrowFunctionToExpression in spec mode\n    // this may also catch .bind(this) written by users, but does it matter? 🤔\n    const { parentPath } = path;\n    if (parentPath.isMemberExpression()) {\n      if (isIdentifier(parentPath.node.property, { name: \"bind\" })) {\n        const { parentPath: bindCall } = parentPath;\n\n        // (function () { ... }).bind(this)()\n\n        return (\n          // first, check if the .bind is actually being called\n          bindCall.isCallExpression() &&\n          // and whether its sole argument is 'this'\n          bindCall.node.arguments.length === 1 &&\n          isThisExpression(bindCall.node.arguments[0]) &&\n          // and whether the result of the .bind(this) is being called\n          bindCall.parentPath.isCallExpression({ callee: bindCall.node })\n        );\n      }\n      return true;\n    }\n\n    return false;\n  }\n}\n"], "mappings": ";;;;;;AACA,IAAAA,mBAAA,GAAAC,OAAA;AACA,IAAAC,qBAAA,GAAAD,OAAA;AACA,IAAAE,KAAA,GAAAF,OAAA;AACA,IAAAG,SAAA,GAAAH,OAAA;AACA,MAAM;EACJI,cAAc;EACdC,SAAS;EACTC,YAAY;EACZC,gBAAgB;EAChBC;AACF,CAAC,GAAGC,WAAC;AAEL,MAAMC,YAAY,GAAGC,kBAAQ,CAACC,kBAAkB,CAA8B;EAC5EC,uBAAuBA,CAACC,IAAI,EAAE;IAC5BA,IAAI,CAACC,IAAI,CAAC,CAAC;EACb,CAAC;EAEDC,eAAeA,CAACF,IAAI,EAAE;IAAEG;EAAU,CAAC,EAAE;IACnC,MAAMC,QAAQ,GAAGJ,IAAI,CAACK,GAAG,CAAC,UAAU,CAAC;IAErCL,IAAI,CAACM,WAAW,CACdZ,eAAe,CACbS,SAAS,GACLb,cAAc,CAACC,SAAS,CAACY,SAAS,CAAC,EAAE,CAACC,QAAQ,CAACG,IAAI,CAAC,CAAC,GACrDH,QAAQ,CAACG,IACf,CACF,CAAC;EACH;AACF,CAAC,CAAC;AAEa,SAAAC,SACbR,IAA0B,EAC1BS,OAGC,EACDC,WAAqB,EACrBC,oBAA8B,EAC9B;EACAX,IAAI,CAACY,QAAQ,CAAChB,YAAY,EAAE;IAC1BO,SAAS,EAAEM,OAAO,CAACN;EACrB,CAAC,CAAC;EAEF,MAAMU,MAAM,GAAGC,WAAW,CAACd,IAAI,CAAC;EAEhCA,IAAI,CAACO,IAAI,CAACQ,KAAK,GAAG,KAAK;EACvBf,IAAI,CAACO,IAAI,CAACS,SAAS,GAAG,IAAI;EAE1B,IAAAC,2BAAY,EACVjB,IAAI,EACJT,SAAS,CAACkB,OAAO,CAACS,SAAS,CAAC,EAC5BR,WAAW,EACXC,oBACF,CAAC;EAED,MAAMQ,UAAU,GACdnB,IAAI,CAACoB,cAAc,CAAC,CAAC,IACrBpB,IAAI,CAACqB,aAAa,CAAC,CAAC,IACpBrB,IAAI,CAACsB,UAAU,CAACC,gBAAgB,CAAC,CAAC,IAClCvB,IAAI,CAACsB,UAAU,CAACE,eAAe,CAAC,CAAC;EAEnC,IAAI,CAACL,UAAU,IAAI,CAACN,MAAM,IAAIb,IAAI,CAACyB,YAAY,CAAC,CAAC,EAAE;IACjD,IAAAC,6BAAc,EAAC1B,IAAI,CAAC;EACtB;EAEA,SAASc,WAAWA,CAACd,IAAc,EAAE;IACnC,IAAIA,IAAI,CAACsB,UAAU,CAACK,gBAAgB,CAAC;MAAEC,MAAM,EAAE5B,IAAI,CAACO;IAAK,CAAC,CAAC,EAAE;MAC3D,OAAO,IAAI;IACb;IAIA,MAAM;MAAEe;IAAW,CAAC,GAAGtB,IAAI;IAC3B,IAAIsB,UAAU,CAACO,kBAAkB,CAAC,CAAC,EAAE;MACnC,IAAIrC,YAAY,CAAC8B,UAAU,CAACf,IAAI,CAACuB,QAAQ,EAAE;QAAEC,IAAI,EAAE;MAAO,CAAC,CAAC,EAAE;QAC5D,MAAM;UAAET,UAAU,EAAEU;QAAS,CAAC,GAAGV,UAAU;QAI3C,QAEEU,QAAQ,CAACL,gBAAgB,CAAC,CAAC,IAE3BK,QAAQ,CAACzB,IAAI,CAAC0B,SAAS,CAACC,MAAM,KAAK,CAAC,IACpCzC,gBAAgB,CAACuC,QAAQ,CAACzB,IAAI,CAAC0B,SAAS,CAAC,CAAC,CAAC,CAAC,IAE5CD,QAAQ,CAACV,UAAU,CAACK,gBAAgB,CAAC;YAAEC,MAAM,EAAEI,QAAQ,CAACzB;UAAK,CAAC;QAAC;MAEnE;MACA,OAAO,IAAI;IACb;IAEA,OAAO,KAAK;EACd;AACF", "ignoreList": []}