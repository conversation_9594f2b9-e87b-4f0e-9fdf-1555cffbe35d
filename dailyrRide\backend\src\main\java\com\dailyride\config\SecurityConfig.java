package com.dailyride.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;

/**
 * Security configuration for the DailyRide application.
 * Provides password encoding and other security-related beans.
 */
@Configuration
public class SecurityConfig {

    /**
     * Password encoder bean for encoding and verifying passwords.
     * Uses BCrypt hashing algorithm which is secure and recommended.
     */
    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }
}
