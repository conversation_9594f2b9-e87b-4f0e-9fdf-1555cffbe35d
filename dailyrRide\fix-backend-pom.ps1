# 🔧 Fix Backend POM.xml Issue
# Resolves Maven Spring Boot plugin not found error

Write-Host "🔧 Fixing Backend POM.xml Issue..." -ForegroundColor Green
Write-Host ""

# Check if we're in the right directory
if (-not (Test-Path "backend\pom-new.xml")) {
    Write-Host "❌ Error: Please run this script from the dailyrRide root directory" -ForegroundColor Red
    Write-Host "   Current directory: $PWD" -ForegroundColor Yellow
    exit 1
}

Set-Location backend

Write-Host "📋 Current situation:" -ForegroundColor Yellow
Write-Host "   ❌ Maven can't find Spring Boot plugin" -ForegroundColor Red
Write-Host "   ❌ POM.xml might be corrupted or missing" -ForegroundColor Red
Write-Host ""

Write-Host "🔧 Applying fix..." -ForegroundColor Cyan

# Remove any corrupted pom.xml
if (Test-Path "pom.xml") {
    Write-Host "🗑️ Removing corrupted pom.xml..." -ForegroundColor Yellow
    Remove-Item "pom.xml" -Force
}

# Copy the fixed version
if (Test-Path "pom-new.xml") {
    Write-Host "✅ Installing fixed pom.xml..." -ForegroundColor Green
    Copy-Item "pom-new.xml" "pom.xml"
    Remove-Item "pom-new.xml" -Force
} else {
    Write-Host "❌ Fixed pom.xml not found!" -ForegroundColor Red
    Write-Host "Please run this script from the dailyrRide root directory." -ForegroundColor Yellow
    exit 1
}

Write-Host ""
Write-Host "✅ POM.xml has been fixed!" -ForegroundColor Green
Write-Host ""

Write-Host "🧪 Testing Maven configuration..." -ForegroundColor Cyan
try {
    mvn --version
    Write-Host "✅ Maven is working correctly" -ForegroundColor Green
} catch {
    Write-Host "⚠️ Maven might not be installed or not in PATH" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "🚀 Now try running the backend:" -ForegroundColor Green
Write-Host "   mvn spring-boot:run" -ForegroundColor White
Write-Host ""
Write-Host "   OR if that fails:" -ForegroundColor Yellow
Write-Host "   mvn clean install" -ForegroundColor White
Write-Host "   mvn spring-boot:run" -ForegroundColor White
Write-Host ""

Set-Location ..
Write-Host "🎉 Backend POM.xml fix completed!" -ForegroundColor Green
Write-Host ""
Write-Host "📊 Summary of changes:" -ForegroundColor Cyan
Write-Host "   ✅ Fixed Spring Boot plugin configuration" -ForegroundColor Green
Write-Host "   ✅ Added explicit plugin version (3.2.0)" -ForegroundColor Green
Write-Host "   ✅ Added Maven compiler plugin" -ForegroundColor Green
Write-Host "   ✅ Set Java version to 17" -ForegroundColor Green
Write-Host ""
Write-Host "Press any key to continue..."
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
