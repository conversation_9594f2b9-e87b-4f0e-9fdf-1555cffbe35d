server:
  port: 8090
  servlet:
    context-path: /api

spring:
  application:
    name: dailyride-backend

  profiles:
    active: development

  # SQLite Database Configuration
  datasource:
    url: ************************
    driver-class-name: org.sqlite.JDBC
    username:
    password:

  jpa:
    database-platform: org.hibernate.community.dialect.SQLiteDialect
    hibernate:
      ddl-auto: update
    show-sql: true
    properties:
      hibernate:
        format_sql: true
        jdbc:
          time_zone: UTC

  security:
    oauth2:
      resourceserver:
        jwt:
          issuer-uri: ${JWT_ISSUER_URI:http://localhost:8090}

  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB

# JWT Configuration
jwt:
  secret: ${JWT_SECRET:your_super_secret_jwt_key_change_this_in_production}
  expiration: ${JWT_EXPIRATION:604800000} # 7 days in milliseconds
  refresh-expiration: ${JWT_REFRESH_EXPIRATION:2592000000} # 30 days in milliseconds

# Google Services Configuration
google:
  maps:
    api-key: ${GOOGLE_MAPS_API_KEY:AIzaSyCtRoF_KnO_3F_n_4lBUnfkRpnAOL38EJc}
  
# Database Configuration
database:
  sync:
    enabled: true
    interval: 30000 # 30 seconds

# CORS Configuration
cors:
  allowed-origins: ${CORS_ALLOWED_ORIGINS:http://localhost:3000,http://localhost:19006,http://localhost:8081,exp://***********:8081}
  allowed-methods: GET,POST,PUT,DELETE,OPTIONS
  allowed-headers: "*"
  allow-credentials: true

# File Upload Configuration
file:
  upload:
    path: ${UPLOAD_PATH:uploads/}
    max-size: ${MAX_FILE_SIZE:5242880} # 5MB

# Logging Configuration
logging:
  level:
    com.dailyride: ${LOG_LEVEL:INFO}
    org.springframework.security: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: ${LOG_FILE:logs/dailyride.log}

# Actuator Configuration
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: when-authorized

# API Documentation
springdoc:
  api-docs:
    path: /api-docs
  swagger-ui:
    path: /swagger-ui.html
    operationsSorter: method
  info:
    title: DailyRide API
    description: API documentation for DailyRide carpooling application
    version: 1.0.0
    contact:
      name: DailyRide Team
      email: <EMAIL>
