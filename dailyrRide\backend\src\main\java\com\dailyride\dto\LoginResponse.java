package com.dailyride.dto;

import com.dailyride.model.User;
import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * Response DTO for login operations.
 * Contains authentication token and user information.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class LoginResponse {
    
    private String token;
    private String tokenType = "Bearer";
    private Long expiresIn; // Token expiration time in seconds
    private User user;
    private String refreshToken;
    
    // Constructors
    public LoginResponse() {}
    
    public LoginResponse(String token, User user, Long expiresIn) {
        this.token = token;
        this.user = user;
        this.expiresIn = expiresIn;
    }
    
    public LoginResponse(String token, User user, Long expiresIn, String refreshToken) {
        this(token, user, expiresIn);
        this.refreshToken = refreshToken;
    }
    
    // Getters and Setters
    public String getToken() {
        return token;
    }
    
    public void setToken(String token) {
        this.token = token;
    }
    
    public String getTokenType() {
        return tokenType;
    }
    
    public void setTokenType(String tokenType) {
        this.tokenType = tokenType;
    }
    
    public Long getExpiresIn() {
        return expiresIn;
    }
    
    public void setExpiresIn(Long expiresIn) {
        this.expiresIn = expiresIn;
    }
    
    public User getUser() {
        return user;
    }
    
    public void setUser(User user) {
        this.user = user;
    }
    
    public String getRefreshToken() {
        return refreshToken;
    }
    
    public void setRefreshToken(String refreshToken) {
        this.refreshToken = refreshToken;
    }
    
    @Override
    public String toString() {
        return "LoginResponse{" +
                "tokenType='" + tokenType + '\'' +
                ", expiresIn=" + expiresIn +
                ", user=" + (user != null ? user.getEmail() : "null") +
                ", hasRefreshToken=" + (refreshToken != null) +
                '}';
    }
}
