# No database configuration needed for Firestore

# Development-specific JWT settings
jwt:
  secret: dev_jwt_secret_key_not_for_production
  expiration: 86400000 # 1 day for development

# Development logging
logging:
  level:
    com.dailyride: DEBUG
    org.springframework.security: DEBUG
    org.springframework.web: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
  pattern:
    console: "%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"

# CORS - Allow all origins in development
cors:
  allowed-origins: "*"

# Development file upload
file:
  upload:
    path: "uploads/"
