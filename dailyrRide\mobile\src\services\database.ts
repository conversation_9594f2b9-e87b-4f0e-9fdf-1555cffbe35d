import * as SQLite from 'expo-sqlite';
import { User, Trip, Booking, RideRequest, Bid } from '@/types';

// Database configuration
const DATABASE_NAME = 'dailyride.db';

class DatabaseService {
  private db: SQLite.SQLiteDatabase | null = null;
  private isInitialized = false;

  async init(): Promise<void> {
    if (this.isInitialized) return;

    try {
      this.db = await SQLite.openDatabaseAsync(DATABASE_NAME);
      await this.createTables();
      await this.runMigrations();
      this.isInitialized = true;
      console.log('✅ Database initialized successfully');
    } catch (error) {
      console.error('❌ Database initialization failed:', error);
      throw error;
    }
  }

  private async createTables(): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');

    // Users table
    await this.db.execAsync(`
      CREATE TABLE IF NOT EXISTS users (
        id TEXT PRIMARY KEY,
        email TEXT UNIQUE NOT NULL,
        password TEXT NOT NULL,
        firstName TEXT NOT NULL,
        lastName TEXT NOT NULL,
        phone TEXT,
        userType TEXT NOT NULL CHECK (userType IN ('PASSENGER', 'DRIVER', 'ADMIN')),
        averageRating REAL DEFAULT 0,
        totalRatings INTEGER DEFAULT 0,
        isVerified INTEGER DEFAULT 0,
        gender TEXT CHECK (gender IN ('MALE', 'FEMALE')),
        bio TEXT,
        profilePhoto TEXT,
        createdAt TEXT NOT NULL,
        updatedAt TEXT NOT NULL
      );
    `);

    // Trips table
    await this.db.execAsync(`
      CREATE TABLE IF NOT EXISTS trips (
        id TEXT PRIMARY KEY,
        driverId TEXT NOT NULL,
        originAddress TEXT NOT NULL,
        originLatitude REAL NOT NULL,
        originLongitude REAL NOT NULL,
        destinationAddress TEXT NOT NULL,
        destinationLatitude REAL NOT NULL,
        destinationLongitude REAL NOT NULL,
        departureDateTime TEXT NOT NULL,
        availableSeats INTEGER NOT NULL,
        pricePerSeat REAL NOT NULL,
        currency TEXT DEFAULT 'MAD',
        description TEXT,
        status TEXT DEFAULT 'ACTIVE' CHECK (status IN ('ACTIVE', 'COMPLETED', 'CANCELLED')),
        vehicleInfo TEXT,
        preferences TEXT,
        createdAt TEXT NOT NULL,
        updatedAt TEXT NOT NULL,
        FOREIGN KEY (driverId) REFERENCES users (id)
      );
    `);

    // Bookings table
    await this.db.execAsync(`
      CREATE TABLE IF NOT EXISTS bookings (
        id TEXT PRIMARY KEY,
        tripId TEXT NOT NULL,
        passengerId TEXT NOT NULL,
        seatsBooked INTEGER NOT NULL,
        totalPrice REAL NOT NULL,
        paymentMethod TEXT NOT NULL,
        status TEXT DEFAULT 'PENDING' CHECK (status IN ('PENDING', 'CONFIRMED', 'COMPLETED', 'CANCELLED')),
        createdAt TEXT NOT NULL,
        updatedAt TEXT NOT NULL,
        FOREIGN KEY (tripId) REFERENCES trips (id),
        FOREIGN KEY (passengerId) REFERENCES users (id)
      );
    `);

    // Ride Requests table
    await this.db.execAsync(`
      CREATE TABLE IF NOT EXISTS ride_requests (
        id TEXT PRIMARY KEY,
        passengerId TEXT NOT NULL,
        originAddress TEXT NOT NULL,
        originLatitude REAL NOT NULL,
        originLongitude REAL NOT NULL,
        destinationAddress TEXT NOT NULL,
        destinationLatitude REAL NOT NULL,
        destinationLongitude REAL NOT NULL,
        departureDateTime TEXT NOT NULL,
        maxPrice REAL NOT NULL,
        seatsNeeded INTEGER NOT NULL,
        description TEXT,
        status TEXT DEFAULT 'PENDING' CHECK (status IN ('PENDING', 'ACCEPTED', 'COMPLETED', 'CANCELLED')),
        createdAt TEXT NOT NULL,
        updatedAt TEXT NOT NULL,
        FOREIGN KEY (passengerId) REFERENCES users (id)
      );
    `);

    // Notifications table
    await this.db.execAsync(`
      CREATE TABLE IF NOT EXISTS notifications (
        id TEXT PRIMARY KEY,
        userId TEXT NOT NULL,
        title TEXT NOT NULL,
        message TEXT NOT NULL,
        type TEXT NOT NULL,
        data TEXT,
        isRead INTEGER DEFAULT 0,
        priority TEXT DEFAULT 'NORMAL' CHECK (priority IN ('LOW', 'NORMAL', 'HIGH', 'URGENT')),
        createdAt TEXT NOT NULL,
        FOREIGN KEY (userId) REFERENCES users (id)
      );
    `);

    // Emergency Contacts table
    await this.db.execAsync(`
      CREATE TABLE IF NOT EXISTS emergency_contacts (
        id TEXT PRIMARY KEY,
        userId TEXT NOT NULL,
        name TEXT NOT NULL,
        phone TEXT NOT NULL,
        relationship TEXT NOT NULL,
        isPrimary INTEGER DEFAULT 0,
        notifyOnTrip INTEGER DEFAULT 1,
        createdAt TEXT NOT NULL,
        FOREIGN KEY (userId) REFERENCES users (id)
      );
    `);

    // Security Alerts table
    await this.db.execAsync(`
      CREATE TABLE IF NOT EXISTS security_alerts (
        id TEXT PRIMARY KEY,
        userId TEXT NOT NULL,
        type TEXT NOT NULL,
        severity TEXT NOT NULL CHECK (severity IN ('LOW', 'MEDIUM', 'HIGH', 'CRITICAL')),
        message TEXT NOT NULL,
        location TEXT,
        resolved INTEGER DEFAULT 0,
        createdAt TEXT NOT NULL,
        FOREIGN KEY (userId) REFERENCES users (id)
      );
    `);

    // Ratings table
    await this.db.execAsync(`
      CREATE TABLE IF NOT EXISTS ratings (
        id TEXT PRIMARY KEY,
        fromUserId TEXT NOT NULL,
        toUserId TEXT NOT NULL,
        tripId TEXT NOT NULL,
        rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
        comment TEXT,
        categories TEXT,
        isAnonymous INTEGER DEFAULT 0,
        createdAt TEXT NOT NULL,
        FOREIGN KEY (fromUserId) REFERENCES users (id),
        FOREIGN KEY (toUserId) REFERENCES users (id),
        FOREIGN KEY (tripId) REFERENCES trips (id)
      );
    `);

    // Badges table
    await this.db.execAsync(`
      CREATE TABLE IF NOT EXISTS badges (
        id TEXT PRIMARY KEY,
        userId TEXT NOT NULL,
        name TEXT NOT NULL,
        description TEXT NOT NULL,
        icon TEXT NOT NULL,
        color TEXT NOT NULL,
        category TEXT NOT NULL,
        earnedAt TEXT NOT NULL,
        FOREIGN KEY (userId) REFERENCES users (id)
      );
    `);

    // Social Interactions table
    await this.db.execAsync(`
      CREATE TABLE IF NOT EXISTS social_interactions (
        id TEXT PRIMARY KEY,
        type TEXT NOT NULL CHECK (type IN ('LIKE', 'FAVORITE', 'BLOCK', 'REPORT')),
        fromUserId TEXT NOT NULL,
        toUserId TEXT NOT NULL,
        reason TEXT,
        createdAt TEXT NOT NULL,
        FOREIGN KEY (fromUserId) REFERENCES users (id),
        FOREIGN KEY (toUserId) REFERENCES users (id)
      );
    `);

    // Bids table
    await this.db.execAsync(`
      CREATE TABLE IF NOT EXISTS bids (
        id TEXT PRIMARY KEY,
        rideRequestId TEXT NOT NULL,
        driverId TEXT NOT NULL,
        proposedPrice REAL NOT NULL,
        message TEXT,
        status TEXT DEFAULT 'PENDING' CHECK (status IN ('PENDING', 'ACCEPTED', 'REJECTED')),
        createdAt TEXT NOT NULL,
        updatedAt TEXT NOT NULL,
        FOREIGN KEY (rideRequestId) REFERENCES ride_requests (id),
        FOREIGN KEY (driverId) REFERENCES users (id)
      );
    `);

    // Driver Verifications table
    await this.db.execAsync(`
      CREATE TABLE IF NOT EXISTS driver_verifications (
        id TEXT PRIMARY KEY,
        driverId TEXT NOT NULL,
        licenseNumber TEXT,
        licensePhoto TEXT,
        vehicleRegistration TEXT,
        vehiclePhoto TEXT,
        facePhoto TEXT,
        status TEXT DEFAULT 'PENDING' CHECK (status IN ('PENDING', 'APPROVED', 'REJECTED')),
        submittedAt TEXT NOT NULL,
        reviewedAt TEXT,
        reviewedBy TEXT,
        notes TEXT,
        FOREIGN KEY (driverId) REFERENCES users (id)
      );
    `);
  }

  private async runMigrations(): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');

    try {
      // Check if migrations table exists
      await this.db.execAsync(`
        CREATE TABLE IF NOT EXISTS migrations (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          version TEXT NOT NULL,
          applied_at TEXT NOT NULL
        );
      `);

      // Get current migration version
      const currentMigration = await this.db.getFirstAsync(
        'SELECT version FROM migrations ORDER BY id DESC LIMIT 1'
      ) as any;

      const currentVersion = currentMigration?.version || '0.0.0';

      // Migration 1.0.1: Add missing columns to existing tables
      if (this.compareVersions(currentVersion, '1.0.1') < 0) {
        console.log('🔄 Running migration 1.0.1: Adding missing columns...');

        // Add departureDateTime to ride_requests if it doesn't exist
        try {
          await this.db.execAsync(`
            ALTER TABLE ride_requests ADD COLUMN departureDateTime TEXT;
          `);
        } catch (error) {
          // Column might already exist, ignore error
        }

        // Add paymentMethod to bookings if it doesn't exist
        try {
          await this.db.execAsync(`
            ALTER TABLE bookings ADD COLUMN paymentMethod TEXT;
          `);
        } catch (error) {
          // Column might already exist, ignore error
        }

        // Record migration
        await this.db.runAsync(
          'INSERT INTO migrations (version, applied_at) VALUES (?, ?)',
          ['1.0.1', new Date().toISOString()]
        );

        console.log('✅ Migration 1.0.1 completed successfully');
      }

      console.log('✅ All migrations completed');
    } catch (error) {
      console.error('❌ Migration failed:', error);
      // Don't throw error, allow app to continue
    }
  }

  private compareVersions(version1: string, version2: string): number {
    const v1parts = version1.split('.').map(Number);
    const v2parts = version2.split('.').map(Number);

    for (let i = 0; i < Math.max(v1parts.length, v2parts.length); i++) {
      const v1part = v1parts[i] || 0;
      const v2part = v2parts[i] || 0;

      if (v1part < v2part) return -1;
      if (v1part > v2part) return 1;
    }

    return 0;
  }

  // User operations
  async createUser(userData: any): Promise<any> {
    if (!this.db) throw new Error('Database not initialized');

    const id = `user-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
    const now = new Date().toISOString();

    await this.db.runAsync(
      `INSERT INTO users (id, email, password, firstName, lastName, phone, userType, averageRating, totalRatings, isVerified, gender, createdAt, updatedAt)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        id,
        userData.email,
        userData.password,
        userData.firstName,
        userData.lastName,
        userData.phone || '',
        userData.userType,
        userData.averageRating || 0,
        userData.totalRatings || 0,
        userData.isVerified ? 1 : 0,
        userData.gender || null,
        now,
        now
      ]
    );

    return this.getUserById(id);
  }

  async authenticateUser(email: string, password: string): Promise<any | null> {
    if (!this.db) throw new Error('Database not initialized');

    const result = await this.db.getFirstAsync(
      'SELECT * FROM users WHERE email = ? AND password = ?',
      [email, password]
    );

    return result ? this.mapRowToUser(result as any) : null;
  }

  async getUserById(id: string): Promise<any | null> {
    if (!this.db) throw new Error('Database not initialized');

    const result = await this.db.getFirstAsync('SELECT * FROM users WHERE id = ?', [id]);
    return result ? this.mapRowToUser(result as any) : null;
  }

  async getUserByEmail(email: string): Promise<any | null> {
    if (!this.db) throw new Error('Database not initialized');

    const result = await this.db.getFirstAsync('SELECT * FROM users WHERE email = ?', [email]);
    return result ? this.mapRowToUser(result as any) : null;
  }

  // Trip operations
  async createTrip(tripData: any): Promise<any> {
    if (!this.db) throw new Error('Database not initialized');

    const id = `trip-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
    const now = new Date().toISOString();

    await this.db.runAsync(
      `INSERT INTO trips (id, driverId, originAddress, originLatitude, originLongitude, destinationAddress, destinationLatitude, destinationLongitude, departureDateTime, availableSeats, pricePerSeat, currency, description, status, vehicleInfo, preferences, createdAt, updatedAt)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        id,
        tripData.driverId,
        tripData.originAddress,
        tripData.originLatitude,
        tripData.originLongitude,
        tripData.destinationAddress,
        tripData.destinationLatitude,
        tripData.destinationLongitude,
        tripData.departureDateTime,
        tripData.availableSeats,
        tripData.pricePerSeat,
        tripData.currency || 'MAD',
        tripData.description || '',
        tripData.status || 'ACTIVE',
        JSON.stringify(tripData.vehicleInfo || {}),
        JSON.stringify(tripData.preferences || {}),
        now,
        now
      ]
    );

    return this.getTripById(id);
  }

  async getAllActiveTrips(): Promise<any[]> {
    if (!this.db) throw new Error('Database not initialized');

    const results = await this.db.getAllAsync(`
      SELECT t.*, u.firstName, u.lastName, u.averageRating, u.totalRatings, u.phone, u.isVerified, u.gender
      FROM trips t
      JOIN users u ON t.driverId = u.id
      WHERE t.status = 'ACTIVE' AND t.availableSeats > 0
      ORDER BY t.departureDateTime ASC
    `);

    return results.map(row => this.mapRowToTrip(row as any));
  }

  async getAllTrips(): Promise<any[]> {
    if (!this.db) throw new Error('Database not initialized');

    const results = await this.db.getAllAsync(`
      SELECT t.*, u.firstName, u.lastName, u.averageRating, u.totalRatings, u.phone, u.isVerified, u.gender
      FROM trips t
      JOIN users u ON t.driverId = u.id
      ORDER BY t.departureDateTime DESC
    `);

    return results.map(row => this.mapRowToTrip(row as any));
  }

  async getTripById(id: string): Promise<any | null> {
    if (!this.db) throw new Error('Database not initialized');

    const result = await this.db.getFirstAsync(`
      SELECT t.*, u.firstName, u.lastName, u.averageRating, u.totalRatings, u.phone, u.isVerified, u.gender
      FROM trips t
      JOIN users u ON t.driverId = u.id
      WHERE t.id = ?
    `, [id]);

    return result ? this.mapRowToTrip(result as any) : null;
  }

  // Helper methods
  private mapRowToUser(row: any): any {
    return {
      id: row.id,
      email: row.email,
      firstName: row.firstName,
      lastName: row.lastName,
      phone: row.phone,
      userType: row.userType,
      averageRating: row.averageRating,
      totalRatings: row.totalRatings,
      isVerified: Boolean(row.isVerified),
      gender: row.gender,
      bio: row.bio,
      profilePhoto: row.profilePhoto,
      createdAt: row.createdAt,
      updatedAt: row.updatedAt,
    };
  }

  private mapRowToTrip(row: any): any {
    return {
      id: row.id,
      driverId: row.driverId,
      driverFirstName: row.firstName,
      driverLastName: row.lastName,
      driverRating: row.averageRating,
      driverGender: row.gender,
      originAddress: row.originAddress,
      originLatitude: row.originLatitude,
      originLongitude: row.originLongitude,
      destinationAddress: row.destinationAddress,
      destinationLatitude: row.destinationLatitude,
      destinationLongitude: row.destinationLongitude,
      departureDateTime: row.departureDateTime,
      availableSeats: row.availableSeats,
      pricePerSeat: row.pricePerSeat,
      currency: row.currency,
      description: row.description,
      status: row.status,
      vehicleInfo: row.vehicleInfo ? JSON.parse(row.vehicleInfo) : {},
      preferences: row.preferences ? JSON.parse(row.preferences) : {},
      createdAt: row.createdAt,
      updatedAt: row.updatedAt,
    };
  }

  // Booking operations
  async createBooking(bookingData: any): Promise<any> {
    if (!this.db) throw new Error('Database not initialized');

    const id = `booking-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
    const now = new Date().toISOString();

    await this.db.runAsync(
      `INSERT INTO bookings (id, tripId, passengerId, seatsBooked, totalPrice, paymentMethod, status, createdAt, updatedAt)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        id,
        bookingData.tripId,
        bookingData.passengerId,
        bookingData.seatsBooked,
        bookingData.totalPrice,
        bookingData.paymentMethod,
        bookingData.status || 'PENDING',
        now,
        now
      ]
    );

    // Update trip available seats
    await this.db.runAsync(
      `UPDATE trips SET availableSeats = availableSeats - ? WHERE id = ?`,
      [bookingData.seatsBooked, bookingData.tripId]
    );

    return { id, ...bookingData, createdAt: now, updatedAt: now };
  }

  async getUserBookings(userId: string): Promise<any[]> {
    if (!this.db) throw new Error('Database not initialized');

    const results = await this.db.getAllAsync(`
      SELECT b.*, t.originAddress as tripOrigin, t.destinationAddress as tripDestination,
             t.departureDateTime, u.firstName, u.lastName
      FROM bookings b
      JOIN trips t ON b.tripId = t.id
      JOIN users u ON t.driverId = u.id
      WHERE b.passengerId = ?
      ORDER BY b.createdAt DESC
    `, [userId]);

    return results.map(row => ({
      ...(row as any),
      tripOrigin: (row as any).tripOrigin,
      tripDestination: (row as any).tripDestination,
    }));
  }

  async getBookingById(id: string): Promise<any | null> {
    if (!this.db) throw new Error('Database not initialized');

    const result = await this.db.getFirstAsync(`
      SELECT b.*, t.originAddress as tripOrigin, t.destinationAddress as tripDestination
      FROM bookings b
      JOIN trips t ON b.tripId = t.id
      WHERE b.id = ?
    `, [id]);

    return result ? {
      ...(result as any),
      tripOrigin: (result as any).tripOrigin,
      tripDestination: (result as any).tripDestination,
    } : null;
  }

  // Ride Request operations
  async createRideRequest(requestData: any): Promise<any> {
    if (!this.db) throw new Error('Database not initialized');

    const id = `request-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
    const now = new Date().toISOString();

    await this.db.runAsync(
      `INSERT INTO ride_requests (id, passengerId, originAddress, originLatitude, originLongitude, destinationAddress, destinationLatitude, destinationLongitude, departureDateTime, maxPrice, seatsNeeded, description, status, createdAt, updatedAt)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        id,
        requestData.passengerId,
        requestData.originAddress,
        requestData.originLatitude,
        requestData.originLongitude,
        requestData.destinationAddress,
        requestData.destinationLatitude,
        requestData.destinationLongitude,
        requestData.departureDateTime,
        requestData.maxPrice,
        requestData.seatsNeeded,
        requestData.description || '',
        requestData.status || 'PENDING',
        now,
        now
      ]
    );

    return { id, ...requestData, createdAt: now, updatedAt: now };
  }

  async getAllRideRequests(): Promise<any[]> {
    if (!this.db) throw new Error('Database not initialized');

    const currentTime = new Date().toISOString();

    const results = await this.db.getAllAsync(`
      SELECT rr.*, u.firstName, u.lastName, u.averageRating, u.totalRatings
      FROM ride_requests rr
      JOIN users u ON rr.passengerId = u.id
      WHERE rr.status = 'PENDING'
        AND rr.departureDateTime > ?
      ORDER BY rr.createdAt DESC
    `, [currentTime]);

    return results.map(row => ({
      ...(row as any),
      passenger: {
        id: (row as any).passengerId,
        firstName: (row as any).firstName,
        lastName: (row as any).lastName,
        averageRating: (row as any).averageRating || 0,
        totalRatings: (row as any).totalRatings || 0,
      },
    }));
  }

  // Admin operations
  async getAllUsers(): Promise<any[]> {
    if (!this.db) throw new Error('Database not initialized');

    const results = await this.db.getAllAsync(`
      SELECT id, email, firstName, lastName, phone, userType, isVerified, averageRating, totalRatings, createdAt
      FROM users
      ORDER BY createdAt DESC
    `);

    return results.map(row => this.mapRowToUser(row as any));
  }

  async updateUser(userId: string, userData: any): Promise<any> {
    if (!this.db) throw new Error('Database not initialized');

    const now = new Date().toISOString();

    await this.db.runAsync(
      `UPDATE users SET
       firstName = ?, lastName = ?, phone = ?, bio = ?,
       profilePhoto = ?, isVerified = ?, updatedAt = ?
       WHERE id = ?`,
      [
        userData.firstName,
        userData.lastName,
        userData.phone,
        userData.bio || null,
        userData.profilePhoto || null,
        userData.isVerified ? 1 : 0,
        now,
        userId
      ]
    );

    return this.getUserById(userId);
  }

  async deleteUser(userId: string): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');

    // Delete user and all related data (cascade)
    await this.db.runAsync('DELETE FROM bookings WHERE passengerId = ?', [userId]);
    await this.db.runAsync('DELETE FROM ride_requests WHERE passengerId = ?', [userId]);
    await this.db.runAsync('DELETE FROM trips WHERE driverId = ?', [userId]);
    await this.db.runAsync('DELETE FROM notifications WHERE userId = ?', [userId]);
    await this.db.runAsync('DELETE FROM emergency_contacts WHERE userId = ?', [userId]);
    await this.db.runAsync('DELETE FROM security_alerts WHERE userId = ?', [userId]);
    await this.db.runAsync('DELETE FROM ratings WHERE fromUserId = ? OR toUserId = ?', [userId, userId]);
    await this.db.runAsync('DELETE FROM badges WHERE userId = ?', [userId]);
    await this.db.runAsync('DELETE FROM social_interactions WHERE fromUserId = ? OR toUserId = ?', [userId, userId]);
    await this.db.runAsync('DELETE FROM users WHERE id = ?', [userId]);
  }

  // Driver limitations
  async canDriverCreateTrip(driverId: string): Promise<{ canCreate: boolean; reason?: string }> {
    if (!this.db) throw new Error('Database not initialized');

    const existingTrips = await this.db.getAllAsync(
      'SELECT * FROM trips WHERE driverId = ? AND status = ?',
      [driverId, 'ACTIVE']
    );

    if (existingTrips.length > 0) {
      const trip = existingTrips[0] as any;
      const departureDate = new Date(trip.departureDateTime).toLocaleDateString();
      const departureTime = new Date(trip.departureDateTime).toLocaleTimeString();

      return {
        canCreate: false,
        reason: `You already have an active trip scheduled for ${departureDate} at ${departureTime}. Please complete or cancel your current trip before creating a new one.`,
      };
    }

    return { canCreate: true };
  }

  async expireOldTrips(): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');

    const currentTime = new Date().toISOString();

    await this.db.runAsync(
      `UPDATE trips SET status = 'COMPLETED'
       WHERE status = 'ACTIVE' AND departureDateTime <= ?`,
      [currentTime]
    );
  }

  // Notification methods
  async createNotification(notificationData: any): Promise<any> {
    if (!this.db) throw new Error('Database not initialized');

    const id = notificationData.id || `notification-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
    const now = new Date().toISOString();

    await this.db.runAsync(
      `INSERT INTO notifications (id, userId, title, message, type, data, isRead, priority, createdAt)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        id,
        notificationData.userId,
        notificationData.title,
        notificationData.message,
        notificationData.type,
        JSON.stringify(notificationData.data || {}),
        notificationData.isRead ? 1 : 0,
        notificationData.priority || 'NORMAL',
        notificationData.createdAt || now
      ]
    );

    return { id, ...notificationData, createdAt: notificationData.createdAt || now };
  }

  async getUserNotifications(userId: string, limit: number = 50): Promise<any[]> {
    if (!this.db) throw new Error('Database not initialized');

    const results = await this.db.getAllAsync(
      `SELECT * FROM notifications
       WHERE userId = ?
       ORDER BY createdAt DESC
       LIMIT ?`,
      [userId, limit]
    );

    return results.map(row => ({
      ...(row as any),
      data: (row as any).data ? JSON.parse((row as any).data) : {},
      isRead: Boolean((row as any).isRead),
    }));
  }

  async markNotificationAsRead(notificationId: string): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');

    await this.db.runAsync(
      'UPDATE notifications SET isRead = 1 WHERE id = ?',
      [notificationId]
    );
  }

  async clearUserNotifications(userId: string): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');

    await this.db.runAsync(
      'DELETE FROM notifications WHERE userId = ?',
      [userId]
    );
  }

  async getUnreadNotificationCount(userId: string): Promise<number> {
    if (!this.db) throw new Error('Database not initialized');

    const result = await this.db.getFirstAsync(
      'SELECT COUNT(*) as count FROM notifications WHERE userId = ? AND isRead = 0',
      [userId]
    );

    return (result as any)?.count || 0;
  }

  // Message methods
  async sendMessage(fromUserId: string, toUserId: string, message: string, tripId?: string): Promise<any> {
    if (!this.db) throw new Error('Database not initialized');

    const id = `message_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const now = new Date().toISOString();

    await this.db.runAsync(
      `INSERT INTO messages (id, fromUserId, toUserId, message, tripId, isRead, createdAt, updatedAt)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        id,
        fromUserId,
        toUserId,
        message,
        tripId || null,
        0, // isRead = false
        now,
        now
      ]
    );

    return {
      id,
      fromUserId,
      toUserId,
      message,
      tripId,
      isRead: false,
      createdAt: now,
      updatedAt: now
    };
  }

  async getMessages(userId1: string, userId2: string, tripId?: string): Promise<any[]> {
    if (!this.db) throw new Error('Database not initialized');

    let query = `
      SELECT m.*, u1.firstName as fromFirstName, u1.lastName as fromLastName,
             u2.firstName as toFirstName, u2.lastName as toLastName
      FROM messages m
      JOIN users u1 ON m.fromUserId = u1.id
      JOIN users u2 ON m.toUserId = u2.id
      WHERE ((m.fromUserId = ? AND m.toUserId = ?) OR (m.fromUserId = ? AND m.toUserId = ?))
    `;

    const params = [userId1, userId2, userId2, userId1];

    if (tripId) {
      query += ' AND m.tripId = ?';
      params.push(tripId);
    }

    query += ' ORDER BY m.createdAt ASC';

    const results = await this.db.getAllAsync(query, params);

    return results.map(row => ({
      ...(row as any),
      isRead: Boolean((row as any).isRead),
    }));
  }

  async getMessagesByTripId(tripId: string): Promise<any[]> {
    if (!this.db) throw new Error('Database not initialized');

    const results = await this.db.getAllAsync(`
      SELECT m.*, u1.firstName as fromFirstName, u1.lastName as fromLastName,
             u2.firstName as toFirstName, u2.lastName as toLastName
      FROM messages m
      JOIN users u1 ON m.fromUserId = u1.id
      JOIN users u2 ON m.toUserId = u2.id
      WHERE m.tripId = ?
      ORDER BY m.createdAt ASC
    `, [tripId]);

    return results.map(row => ({
      ...(row as any),
      isRead: Boolean((row as any).isRead),
    }));
  }

  // Search methods
  async searchTripsAdvanced(query: string, params: any[]): Promise<any[]> {
    if (!this.db) throw new Error('Database not initialized');

    const results = await this.db.getAllAsync(query, params);
    return results.map(row => this.mapRowToTrip(row as any));
  }

  async executeQuery(query: string, params: any[] = []): Promise<any[]> {
    if (!this.db) throw new Error('Database not initialized');

    return await this.db.getAllAsync(query, params);
  }

  async getSearchSuggestions(query: string, limit: number = 5): Promise<string[]> {
    if (!this.db) throw new Error('Database not initialized');

    const results = await this.db.getAllAsync(`
      SELECT DISTINCT originAddress as suggestion FROM trips
      WHERE originAddress LIKE ? AND status = 'ACTIVE'
      UNION
      SELECT DISTINCT destinationAddress as suggestion FROM trips
      WHERE destinationAddress LIKE ? AND status = 'ACTIVE'
      LIMIT ?
    `, [`%${query}%`, `%${query}%`, limit]);

    return results.map(row => (row as any).suggestion);
  }

  async getPopularSearchTerms(limit: number = 10): Promise<any[]> {
    if (!this.db) throw new Error('Database not initialized');

    return await this.db.getAllAsync(`
      SELECT
        originAddress,
        destinationAddress,
        COUNT(*) as searchCount
      FROM trips
      WHERE status = 'ACTIVE'
      GROUP BY originAddress, destinationAddress
      ORDER BY searchCount DESC
      LIMIT ?
    `, [limit]);
  }

  // Security methods
  async createEmergencyContact(userId: string, contact: any): Promise<any> {
    if (!this.db) throw new Error('Database not initialized');

    const now = new Date().toISOString();

    await this.db.runAsync(
      `INSERT INTO emergency_contacts (id, userId, name, phone, relationship, isPrimary, notifyOnTrip, createdAt)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        contact.id,
        userId,
        contact.name,
        contact.phone,
        contact.relationship,
        contact.isPrimary ? 1 : 0,
        contact.notifyOnTrip ? 1 : 0,
        now
      ]
    );

    return { ...contact, userId, createdAt: now };
  }

  async getEmergencyContacts(userId: string): Promise<any[]> {
    if (!this.db) throw new Error('Database not initialized');

    const results = await this.db.getAllAsync(
      'SELECT * FROM emergency_contacts WHERE userId = ? ORDER BY isPrimary DESC, name ASC',
      [userId]
    );

    return results.map(row => ({
      ...(row as any),
      isPrimary: Boolean((row as any).isPrimary),
      notifyOnTrip: Boolean((row as any).notifyOnTrip),
    }));
  }

  async createSecurityAlert(alert: any): Promise<any> {
    if (!this.db) throw new Error('Database not initialized');

    const now = new Date().toISOString();

    await this.db.runAsync(
      `INSERT INTO security_alerts (id, userId, type, severity, message, location, resolved, createdAt)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        alert.id,
        alert.userId,
        alert.type,
        alert.severity,
        alert.message,
        alert.location ? JSON.stringify(alert.location) : null,
        alert.resolved ? 1 : 0,
        alert.timestamp || now
      ]
    );

    return { ...alert, createdAt: alert.timestamp || now };
  }

  async getSecurityAlerts(userId: string, limit: number = 50): Promise<any[]> {
    if (!this.db) throw new Error('Database not initialized');

    const results = await this.db.getAllAsync(
      `SELECT * FROM security_alerts
       WHERE userId = ?
       ORDER BY createdAt DESC
       LIMIT ?`,
      [userId, limit]
    );

    return results.map(row => ({
      ...(row as any),
      location: (row as any).location ? JSON.parse((row as any).location) : null,
      resolved: Boolean((row as any).resolved),
    }));
  }

  // Social service methods
  async createRating(rating: any): Promise<any> {
    if (!this.db) throw new Error('Database not initialized');

    await this.db.runAsync(
      `INSERT INTO ratings (id, fromUserId, toUserId, tripId, rating, comment, categories, isAnonymous, createdAt)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        rating.id,
        rating.fromUserId,
        rating.toUserId,
        rating.tripId,
        rating.rating,
        rating.comment || null,
        JSON.stringify(rating.categories || []),
        rating.isAnonymous ? 1 : 0,
        rating.createdAt
      ]
    );

    return rating;
  }

  async getUserRatings(userId: string, limit: number = 20): Promise<any[]> {
    if (!this.db) throw new Error('Database not initialized');

    const results = await this.db.getAllAsync(
      `SELECT r.*, u.firstName, u.lastName
       FROM ratings r
       JOIN users u ON r.fromUserId = u.id
       WHERE r.toUserId = ?
       ORDER BY r.createdAt DESC
       LIMIT ?`,
      [userId, limit]
    );

    return results.map(row => ({
      ...(row as any),
      categories: (row as any).categories ? JSON.parse((row as any).categories) : [],
      isAnonymous: Boolean((row as any).isAnonymous),
    }));
  }

  async updateUserRating(userId: string, averageRating: number, totalRatings: number): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');

    await this.db.runAsync(
      'UPDATE users SET averageRating = ?, totalRatings = ?, updatedAt = ? WHERE id = ?',
      [averageRating, totalRatings, new Date().toISOString(), userId]
    );
  }

  async awardBadge(userId: string, badge: any): Promise<any> {
    if (!this.db) throw new Error('Database not initialized');

    await this.db.runAsync(
      `INSERT INTO badges (id, userId, name, description, icon, color, category, earnedAt)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        badge.id,
        userId,
        badge.name,
        badge.description,
        badge.icon,
        badge.color,
        badge.category,
        badge.earnedAt
      ]
    );

    return badge;
  }

  async getUserBadges(userId: string): Promise<any[]> {
    if (!this.db) throw new Error('Database not initialized');

    return await this.db.getAllAsync(
      'SELECT * FROM badges WHERE userId = ? ORDER BY earnedAt DESC',
      [userId]
    );
  }

  async createSocialInteraction(interaction: any): Promise<any> {
    if (!this.db) throw new Error('Database not initialized');

    await this.db.runAsync(
      `INSERT INTO social_interactions (id, type, fromUserId, toUserId, reason, createdAt)
       VALUES (?, ?, ?, ?, ?, ?)`,
      [
        interaction.id,
        interaction.type,
        interaction.fromUserId,
        interaction.toUserId,
        interaction.reason || null,
        interaction.createdAt
      ]
    );

    return interaction;
  }

  async getSocialInteractions(userId: string, type?: string): Promise<any[]> {
    if (!this.db) throw new Error('Database not initialized');

    let query = 'SELECT * FROM social_interactions WHERE fromUserId = ?';
    const params = [userId];

    if (type) {
      query += ' AND type = ?';
      params.push(type);
    }

    query += ' ORDER BY createdAt DESC';

    return await this.db.getAllAsync(query, params);
  }

  async getUserStats(userId: string): Promise<any> {
    if (!this.db) throw new Error('Database not initialized');

    // Get trip statistics
    const tripStats = await this.db.getFirstAsync(`
      SELECT
        COUNT(*) as totalTrips,
        SUM(CASE WHEN status = 'COMPLETED' THEN 1 ELSE 0 END) as completedTrips,
        AVG(pricePerSeat) as avgPrice
      FROM trips
      WHERE driverId = ? OR id IN (
        SELECT tripId FROM bookings WHERE passengerId = ?
      )
    `, [userId, userId]);

    // Calculate other stats (simplified)
    return {
      totalTrips: (tripStats as any)?.totalTrips || 0,
      totalDistance: ((tripStats as any)?.totalTrips || 0) * 100, // Estimated
      carbonSaved: ((tripStats as any)?.totalTrips || 0) * 5, // Estimated kg CO2
      moneySaved: ((tripStats as any)?.totalTrips || 0) * 50, // Estimated MAD
      onTimePercentage: 95, // Default
      responseRate: 98, // Default
      completionRate: ((tripStats as any)?.completedTrips || 0) / Math.max((tripStats as any)?.totalTrips || 1, 1) * 100,
      favoriteRoutes: [],
    };
  }

  async getUserPreferences(userId: string): Promise<any> {
    if (!this.db) throw new Error('Database not initialized');

    // Get user preferences (simplified - could be stored in separate table)
    return {
      musicGenres: ['Pop', 'Rock'],
      conversationLevel: 'MODERATE',
      smokingTolerance: false,
      petTolerance: false,
      temperaturePreference: 'MODERATE',
    };
  }

  async getVerificationStatus(userId: string): Promise<any> {
    if (!this.db) throw new Error('Database not initialized');

    const user = await this.getUserById(userId);
    return {
      identity: user?.isVerified || false,
      phone: true, // Assume phone is verified during registration
      email: true, // Assume email is verified during registration
      drivingLicense: user?.userType === 'DRIVER' ? user?.isVerified : undefined,
      vehicle: user?.userType === 'DRIVER' ? user?.isVerified : undefined,
      background: user?.isVerified || false,
    };
  }

  async updateUserProfile(userId: string, updates: any): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');

    const setClause = [];
    const params = [];

    if (updates.bio !== undefined) {
      setClause.push('bio = ?');
      params.push(updates.bio);
    }

    if (updates.profilePhoto !== undefined) {
      setClause.push('profilePhoto = ?');
      params.push(updates.profilePhoto);
    }

    if (setClause.length > 0) {
      params.push(new Date().toISOString());
      params.push(userId);

      await this.db.runAsync(
        `UPDATE users SET ${setClause.join(', ')}, updatedAt = ? WHERE id = ?`,
        params
      );
    }
  }
}

export const databaseService = new DatabaseService();
