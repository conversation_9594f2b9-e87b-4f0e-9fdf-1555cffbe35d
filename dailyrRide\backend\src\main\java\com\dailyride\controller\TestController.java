package com.dailyride.controller;

import com.dailyride.model.User;
import com.dailyride.model.Trip;
import com.dailyride.repository.UserJpaRepository;
import com.dailyride.repository.TripJpaRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.HashMap;

/**
 * Test controller to verify backend-frontend integration.
 */
@RestController
@RequestMapping("/api/test")
@CrossOrigin(origins = "*")
public class TestController {

    @Autowired
    private UserJpaRepository userRepository;

    @Autowired
    private TripJpaRepository tripRepository;

    /**
     * Health check endpoint.
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> health() {
        Map<String, Object> response = new HashMap<>();
        response.put("status", "OK");
        response.put("message", "Backend is running with SQLite");
        response.put("timestamp", System.currentTimeMillis());
        return ResponseEntity.ok(response);
    }

    /**
     * Get all users.
     */
    @GetMapping("/users")
    public ResponseEntity<List<User>> getAllUsers() {
        List<User> users = userRepository.findAll();
        return ResponseEntity.ok(users);
    }

    /**
     * Get all trips.
     */
    @GetMapping("/trips")
    public ResponseEntity<List<Trip>> getAllTrips() {
        List<Trip> trips = tripRepository.findAll();
        return ResponseEntity.ok(trips);
    }

    /**
     * Create a test user.
     */
    @PostMapping("/users")
    public ResponseEntity<User> createTestUser(@RequestBody Map<String, String> userData) {
        User user = new User();
        user.setId("test-user-" + System.currentTimeMillis());
        user.setEmail(userData.getOrDefault("email", "<EMAIL>"));
        user.setPassword("password123");
        user.setFirstName(userData.getOrDefault("firstName", "Test"));
        user.setLastName(userData.getOrDefault("lastName", "User"));
        user.setPhone(userData.getOrDefault("phone", "+212600000000"));
        user.setUserType(User.UserType.valueOf(userData.getOrDefault("userType", "PASSENGER")));
        user.setIsVerified(true);
        user.setCreatedAt(java.time.LocalDateTime.now().toString());
        user.setUpdatedAt(java.time.LocalDateTime.now().toString());

        User savedUser = userRepository.save(user);
        return ResponseEntity.ok(savedUser);
    }

    /**
     * Create a test trip.
     */
    @PostMapping("/trips")
    public ResponseEntity<Trip> createTestTrip(@RequestBody Map<String, Object> tripData) {
        Trip trip = new Trip();
        trip.setId("test-trip-" + System.currentTimeMillis());
        trip.setDriverId((String) tripData.getOrDefault("driverId", "test-driver-1"));
        trip.setOriginAddress((String) tripData.getOrDefault("originAddress", "Casablanca, Morocco"));
        trip.setOriginLatitude(33.5731);
        trip.setOriginLongitude(-7.5898);
        trip.setDestinationAddress((String) tripData.getOrDefault("destinationAddress", "Rabat, Morocco"));
        trip.setDestinationLatitude(34.0209);
        trip.setDestinationLongitude(-6.8416);
        trip.setDepartureDateTime(java.time.LocalDateTime.now().plusHours(2).toString());
        trip.setAvailableSeats(3);
        trip.setPricePerSeat(50.0);
        trip.setCurrency("MAD");
        trip.setDescription("Test trip");
        trip.setStatus(Trip.TripStatus.ACTIVE);
        trip.setCreatedAt(java.time.LocalDateTime.now().toString());
        trip.setUpdatedAt(java.time.LocalDateTime.now().toString());

        Trip savedTrip = tripRepository.save(trip);
        return ResponseEntity.ok(savedTrip);
    }

    /**
     * Database statistics.
     */
    @GetMapping("/stats")
    public ResponseEntity<Map<String, Object>> getStats() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("totalUsers", userRepository.count());
        stats.put("totalTrips", tripRepository.count());
        stats.put("activeTrips", tripRepository.countByStatus(Trip.TripStatus.ACTIVE));
        stats.put("drivers", userRepository.countByUserType(User.UserType.DRIVER));
        stats.put("passengers", userRepository.countByUserType(User.UserType.PASSENGER));
        stats.put("admins", userRepository.countByUserType(User.UserType.ADMIN));
        return ResponseEntity.ok(stats);
    }

    /**
     * Test authentication endpoint.
     */
    @PostMapping("/auth")
    public ResponseEntity<Map<String, Object>> testAuth(@RequestBody Map<String, String> credentials) {
        String email = credentials.get("email");
        String password = credentials.get("password");

        User user = userRepository.findByEmail(email).orElse(null);
        
        Map<String, Object> response = new HashMap<>();
        if (user != null && "password123".equals(password)) {
            response.put("success", true);
            response.put("user", user);
            response.put("token", "test-jwt-token-" + System.currentTimeMillis());
        } else {
            response.put("success", false);
            response.put("message", "Invalid credentials");
        }
        
        return ResponseEntity.ok(response);
    }

    /**
     * Seed database with test data.
     */
    @PostMapping("/seed")
    public ResponseEntity<Map<String, Object>> seedDatabase() {
        // Create test users
        User driver = new User();
        driver.setId("driver-1");
        driver.setEmail("<EMAIL>");
        driver.setPassword("password123");
        driver.setFirstName("Ahmed");
        driver.setLastName("Benali");
        driver.setPhone("+212600000001");
        driver.setUserType(User.UserType.DRIVER);
        driver.setIsVerified(true);
        driver.setAverageRating(4.5);
        driver.setTotalRatings(20);
        driver.setCreatedAt(java.time.LocalDateTime.now().toString());
        driver.setUpdatedAt(java.time.LocalDateTime.now().toString());
        userRepository.save(driver);

        User passenger = new User();
        passenger.setId("passenger-1");
        passenger.setEmail("<EMAIL>");
        passenger.setPassword("password123");
        passenger.setFirstName("Fatima");
        passenger.setLastName("Zahra");
        passenger.setPhone("+212600000002");
        passenger.setUserType(User.UserType.PASSENGER);
        passenger.setIsVerified(true);
        passenger.setAverageRating(4.8);
        passenger.setTotalRatings(15);
        passenger.setCreatedAt(java.time.LocalDateTime.now().toString());
        passenger.setUpdatedAt(java.time.LocalDateTime.now().toString());
        userRepository.save(passenger);

        User admin = new User();
        admin.setId("admin-1");
        admin.setEmail("<EMAIL>");
        admin.setPassword("password123");
        admin.setFirstName("Admin");
        admin.setLastName("User");
        admin.setPhone("+212600000003");
        admin.setUserType(User.UserType.ADMIN);
        admin.setIsVerified(true);
        admin.setCreatedAt(java.time.LocalDateTime.now().toString());
        admin.setUpdatedAt(java.time.LocalDateTime.now().toString());
        userRepository.save(admin);

        // Create test trip
        Trip trip = new Trip();
        trip.setId("trip-1");
        trip.setDriverId("driver-1");
        trip.setOriginAddress("Casablanca Marina, Morocco");
        trip.setOriginLatitude(33.6084);
        trip.setOriginLongitude(-7.6306);
        trip.setDestinationAddress("Mohammed V Airport, Morocco");
        trip.setDestinationLatitude(33.3673);
        trip.setDestinationLongitude(-7.5898);
        trip.setDepartureDateTime(java.time.LocalDateTime.now().plusHours(3).toString());
        trip.setAvailableSeats(3);
        trip.setPricePerSeat(80.0);
        trip.setCurrency("MAD");
        trip.setDescription("Airport transfer from Marina");
        trip.setStatus(Trip.TripStatus.ACTIVE);
        trip.setCreatedAt(java.time.LocalDateTime.now().toString());
        trip.setUpdatedAt(java.time.LocalDateTime.now().toString());
        tripRepository.save(trip);

        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("message", "Database seeded with test data");
        response.put("usersCreated", 3);
        response.put("tripsCreated", 1);
        
        return ResponseEntity.ok(response);
    }
}
