@echo off
echo 🔧 Fixing Backend POM.xml Issue...
echo.

cd backend

echo 📋 Current situation:
echo   ❌ Maven can't find Spring Boot plugin
echo   ❌ POM.xml might be corrupted or missing
echo.

echo 🔧 Applying fix...

REM Remove any corrupted pom.xml
if exist pom.xml (
    echo 🗑️ Removing corrupted pom.xml...
    del pom.xml
)

REM Copy the fixed version
if exist pom-new.xml (
    echo ✅ Installing fixed pom.xml...
    copy pom-new.xml pom.xml
    del pom-new.xml
) else (
    echo ❌ Fixed pom.xml not found!
    echo Please run this script from the dailyrRide root directory.
    pause
    exit /b 1
)

echo.
echo ✅ POM.xml has been fixed!
echo.
echo 🧪 Testing Maven configuration...
mvn --version
echo.

echo 🚀 Now try running the backend:
echo   mvn spring-boot:run
echo.
echo   OR
echo.
echo   mvn clean install
echo   mvn spring-boot:run
echo.

cd ..
echo 🎉 Backend POM.xml fix completed!
pause
