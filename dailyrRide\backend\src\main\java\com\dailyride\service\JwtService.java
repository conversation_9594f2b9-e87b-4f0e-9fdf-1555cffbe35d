package com.dailyride.service;

import com.dailyride.exception.AuthenticationExceptions.*;
import com.dailyride.model.User;
import io.jsonwebtoken.*;
import io.jsonwebtoken.io.Decoders;
import io.jsonwebtoken.security.Keys;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.stereotype.Service;

import java.security.Key;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.function.Function;

/**
 * Service for JWT token generation, validation, and parsing.
 */
@Service
public class JwtService {

    private static final Logger logger = LoggerFactory.getLogger(JwtService.class);

    @Value("${jwt.secret:404E635266556A586E3272357538782F413F4428472B4B6250645367566B5970}")
    private String secretKey;

    @Value("${jwt.expiration:86400000}") // 24 hours in milliseconds
    private long jwtExpiration;

    @Value("${jwt.refresh-expiration:604800000}") // 7 days in milliseconds
    private long refreshExpiration;

    /**
     * Generate JWT token for user.
     */
    public String generateToken(User user) {
        return generateToken(new HashMap<>(), user);
    }

    /**
     * Generate JWT token with extra claims.
     */
    public String generateToken(Map<String, Object> extraClaims, User user) {
        return buildToken(extraClaims, user, jwtExpiration);
    }

    /**
     * Generate refresh token for user.
     */
    public String generateRefreshToken(User user) {
        return buildToken(new HashMap<>(), user, refreshExpiration);
    }

    /**
     * Extract username (user ID) from token.
     */
    public String extractUsername(String token) {
        return extractClaim(token, Claims::getSubject);
    }

    /**
     * Extract user ID from token.
     */
    public String extractUserId(String token) {
        return extractClaim(token, claims -> claims.get("userId", String.class));
    }

    /**
     * Extract user type from token.
     */
    public String extractUserType(String token) {
        return extractClaim(token, claims -> claims.get("userType", String.class));
    }

    /**
     * Extract expiration date from token.
     */
    public Date extractExpiration(String token) {
        return extractClaim(token, Claims::getExpiration);
    }

    /**
     * Extract specific claim from token.
     */
    public <T> T extractClaim(String token, Function<Claims, T> claimsResolver) {
        final Claims claims = extractAllClaims(token);
        return claimsResolver.apply(claims);
    }

    /**
     * Validate token against user details.
     */
    public boolean isTokenValid(String token, UserDetails userDetails) {
        try {
            final String username = extractUsername(token);
            return (username.equals(userDetails.getUsername())) && !isTokenExpired(token);
        } catch (Exception e) {
            logger.warn("Token validation failed: {}", e.getMessage());
            return false;
        }
    }

    /**
     * Validate token against user.
     */
    public boolean isTokenValid(String token, User user) {
        try {
            final String userId = extractUserId(token);
            return (userId.equals(user.getId())) && !isTokenExpired(token);
        } catch (Exception e) {
            logger.warn("Token validation failed: {}", e.getMessage());
            return false;
        }
    }

    /**
     * Check if token is expired.
     */
    public boolean isTokenExpired(String token) {
        return extractExpiration(token).before(new Date());
    }

    /**
     * Validate token and throw appropriate exceptions.
     */
    public void validateToken(String token) {
        try {
            extractAllClaims(token);
        } catch (ExpiredJwtException e) {
            logger.warn("JWT token expired: {}", e.getMessage());
            throw new TokenExpiredException();
        } catch (UnsupportedJwtException e) {
            logger.warn("JWT token unsupported: {}", e.getMessage());
            throw new InvalidTokenException("Unsupported JWT token");
        } catch (MalformedJwtException e) {
            logger.warn("JWT token malformed: {}", e.getMessage());
            throw new InvalidTokenException("Malformed JWT token");
        } catch (SignatureException e) {
            logger.warn("JWT signature invalid: {}", e.getMessage());
            throw new InvalidTokenException("Invalid JWT signature");
        } catch (IllegalArgumentException e) {
            logger.warn("JWT token illegal argument: {}", e.getMessage());
            throw new InvalidTokenException("JWT token compact of handler are invalid");
        }
    }

    /**
     * Get remaining time until token expires (in milliseconds).
     */
    public long getTokenRemainingTime(String token) {
        Date expiration = extractExpiration(token);
        return expiration.getTime() - System.currentTimeMillis();
    }

    /**
     * Check if token needs refresh (expires within 1 hour).
     */
    public boolean needsRefresh(String token) {
        long remainingTime = getTokenRemainingTime(token);
        return remainingTime < 3600000; // 1 hour in milliseconds
    }

    /**
     * Refresh token if valid and not expired.
     */
    public String refreshToken(String token, User user) {
        validateToken(token);
        
        if (isTokenExpired(token)) {
            throw new TokenExpiredException();
        }
        
        // Generate new token with same claims
        Map<String, Object> claims = new HashMap<>();
        Claims existingClaims = extractAllClaims(token);
        claims.put("userId", existingClaims.get("userId"));
        claims.put("userType", existingClaims.get("userType"));
        claims.put("email", existingClaims.get("email"));
        
        return generateToken(claims, user);
    }

    // Private helper methods

    private String buildToken(Map<String, Object> extraClaims, User user, long expiration) {
        Map<String, Object> claims = new HashMap<>(extraClaims);
        claims.put("userId", user.getId());
        claims.put("userType", user.getUserType().name());
        claims.put("email", user.getEmail());
        claims.put("isVerified", user.getIsVerified());
        claims.put("isActive", user.getIsVerified());

        return Jwts.builder()
                .setClaims(claims)
                .setSubject(user.getId())
                .setIssuedAt(new Date(System.currentTimeMillis()))
                .setExpiration(new Date(System.currentTimeMillis() + expiration))
                .signWith(getSignInKey(), SignatureAlgorithm.HS256)
                .compact();
    }

    private Claims extractAllClaims(String token) {
        return Jwts.parser()
                .setSigningKey(getSignInKey())
                .build()
                .parseClaimsJws(token)
                .getBody();
    }

    private Key getSignInKey() {
        byte[] keyBytes = Decoders.BASE64.decode(secretKey);
        return Keys.hmacShaKeyFor(keyBytes);
    }
}
