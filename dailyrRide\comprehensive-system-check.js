#!/usr/bin/env node

/**
 * 🔍 DailyRide - Comprehensive System Check
 * Verifies all components are working correctly and in sync
 * Optimized for Expo SDK 53
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🚗 DailyRide - Comprehensive System Check');
console.log('=' .repeat(50));

const checks = [];
let passedChecks = 0;
let totalChecks = 0;

function addCheck(name, status, details = '') {
  checks.push({ name, status, details });
  totalChecks++;
  if (status === '✅') passedChecks++;
}

function runCommand(command, cwd = process.cwd()) {
  try {
    const result = execSync(command, { 
      cwd, 
      encoding: 'utf8', 
      stdio: 'pipe' 
    });
    return { success: true, output: result.trim() };
  } catch (error) {
    return { success: false, error: error.message };
  }
}

// 1. Check Expo SDK 53 Configuration
console.log('\n📱 Checking Expo SDK 53 Configuration...');
try {
  const packageJson = JSON.parse(fs.readFileSync('./mobile/package.json', 'utf8'));
  const expoVersion = packageJson.dependencies.expo;
  const reactVersion = packageJson.dependencies.react;
  const reactNativeVersion = packageJson.dependencies['react-native'];
  
  if (expoVersion.includes('53.')) {
    addCheck('Expo SDK Version', '✅', `v${expoVersion} (SDK 53)`);
  } else {
    addCheck('Expo SDK Version', '❌', `v${expoVersion} (Expected SDK 53)`);
  }
  
  if (reactVersion === '19.0.0') {
    addCheck('React Version', '✅', `v${reactVersion} (Compatible with SDK 53)`);
  } else {
    addCheck('React Version', '⚠️', `v${reactVersion} (Expected 19.0.0)`);
  }
  
  if (reactNativeVersion === '0.79.2') {
    addCheck('React Native Version', '✅', `v${reactNativeVersion} (Compatible with SDK 53)`);
  } else {
    addCheck('React Native Version', '⚠️', `v${reactNativeVersion} (Expected 0.79.2)`);
  }
} catch (error) {
  addCheck('Package.json Reading', '❌', error.message);
}

// 2. Check App Configuration
console.log('\n⚙️ Checking App Configuration...');
try {
  const appJson = JSON.parse(fs.readFileSync('./mobile/app.json', 'utf8'));
  
  if (appJson.expo && appJson.expo.name) {
    addCheck('App.json Structure', '✅', `App: ${appJson.expo.name}`);
  } else {
    addCheck('App.json Structure', '❌', 'Invalid app.json structure');
  }
  
  // Check for required permissions
  const androidPermissions = appJson.expo?.android?.permissions || [];
  const requiredPermissions = ['ACCESS_FINE_LOCATION', 'ACCESS_COARSE_LOCATION'];
  const hasLocationPermissions = requiredPermissions.every(perm => 
    androidPermissions.includes(perm)
  );
  
  if (hasLocationPermissions) {
    addCheck('Location Permissions', '✅', 'All required permissions configured');
  } else {
    addCheck('Location Permissions', '❌', 'Missing location permissions');
  }
} catch (error) {
  addCheck('App.json Reading', '❌', error.message);
}

// 3. Check TypeScript Configuration
console.log('\n📝 Checking TypeScript Configuration...');
try {
  const tsConfig = JSON.parse(fs.readFileSync('./mobile/tsconfig.json', 'utf8'));
  
  if (tsConfig.compilerOptions && tsConfig.compilerOptions.baseUrl) {
    addCheck('TypeScript Config', '✅', 'Valid tsconfig.json');
  } else {
    addCheck('TypeScript Config', '❌', 'Invalid tsconfig.json');
  }
  
  // Check for path aliases
  const paths = tsConfig.compilerOptions?.paths;
  if (paths && paths['@/*']) {
    addCheck('Path Aliases', '✅', 'Path aliases configured');
  } else {
    addCheck('Path Aliases', '❌', 'Path aliases missing');
  }
} catch (error) {
  addCheck('TypeScript Config', '❌', error.message);
}

// 4. Check Backend Configuration
console.log('\n🔧 Checking Backend Configuration...');
try {
  if (fs.existsSync('./backend/pom.xml')) {
    const pomContent = fs.readFileSync('./backend/pom.xml', 'utf8');
    
    if (pomContent.includes('<groupId>com.dailyride</groupId>')) {
      addCheck('Backend POM Structure', '✅', 'Valid Maven configuration');
    } else {
      addCheck('Backend POM Structure', '❌', 'Invalid Maven configuration');
    }
    
    // Check for malformed XML
    if (pomContent.includes('<n>') && !pomContent.includes('<name>')) {
      addCheck('POM XML Validation', '⚠️', 'Found malformed <n> tag, should be <name>');
    } else {
      addCheck('POM XML Validation', '✅', 'XML structure is valid');
    }
  } else {
    addCheck('Backend POM File', '❌', 'pom.xml not found');
  }
} catch (error) {
  addCheck('Backend Configuration', '❌', error.message);
}

// 5. Check Dependencies Compatibility
console.log('\n📦 Checking Dependencies Compatibility...');
try {
  const packageJson = JSON.parse(fs.readFileSync('./mobile/package.json', 'utf8'));
  const deps = packageJson.dependencies;
  
  // Check critical dependencies for SDK 53 compatibility
  const criticalDeps = {
    '@react-navigation/native': '^6.1.9',
    '@react-navigation/bottom-tabs': '^6.5.11',
    '@react-navigation/stack': '^6.3.20',
    'react-native-maps': '1.20.1',
    'react-native-reanimated': '~3.17.4'
  };
  
  let compatibleDeps = 0;
  let totalCriticalDeps = Object.keys(criticalDeps).length;
  
  for (const [dep, expectedVersion] of Object.entries(criticalDeps)) {
    if (deps[dep]) {
      compatibleDeps++;
    }
  }
  
  if (compatibleDeps === totalCriticalDeps) {
    addCheck('Critical Dependencies', '✅', `${compatibleDeps}/${totalCriticalDeps} compatible`);
  } else {
    addCheck('Critical Dependencies', '⚠️', `${compatibleDeps}/${totalCriticalDeps} compatible`);
  }
} catch (error) {
  addCheck('Dependencies Check', '❌', error.message);
}

// 6. Check File Structure
console.log('\n📁 Checking File Structure...');
const requiredFiles = [
  './mobile/App.tsx',
  './mobile/src/store/index.ts',
  './mobile/src/navigation/AppNavigator.tsx',
  './mobile/src/services/database.ts',
  './mobile/src/constants/config.ts'
];

let existingFiles = 0;
requiredFiles.forEach(file => {
  if (fs.existsSync(file)) {
    existingFiles++;
  }
});

if (existingFiles === requiredFiles.length) {
  addCheck('File Structure', '✅', `${existingFiles}/${requiredFiles.length} required files present`);
} else {
  addCheck('File Structure', '⚠️', `${existingFiles}/${requiredFiles.length} required files present`);
}

// 7. Check for Common Issues
console.log('\n🔍 Checking for Common Issues...');

// Check for duplicate dependencies
try {
  const packageJson = JSON.parse(fs.readFileSync('./mobile/package.json', 'utf8'));
  const deps = Object.keys(packageJson.dependencies || {});
  const devDeps = Object.keys(packageJson.devDependencies || {});
  const duplicates = deps.filter(dep => devDeps.includes(dep));
  
  if (duplicates.length === 0) {
    addCheck('Duplicate Dependencies', '✅', 'No duplicate dependencies found');
  } else {
    addCheck('Duplicate Dependencies', '⚠️', `Found duplicates: ${duplicates.join(', ')}`);
  }
} catch (error) {
  addCheck('Duplicate Dependencies Check', '❌', error.message);
}

// Print Results
console.log('\n' + '='.repeat(50));
console.log('📊 SYSTEM CHECK RESULTS');
console.log('='.repeat(50));

checks.forEach(check => {
  console.log(`${check.status} ${check.name}`);
  if (check.details) {
    console.log(`   ${check.details}`);
  }
});

console.log('\n' + '='.repeat(50));
console.log(`📈 SUMMARY: ${passedChecks}/${totalChecks} checks passed`);

const percentage = Math.round((passedChecks / totalChecks) * 100);
if (percentage >= 90) {
  console.log('🎉 EXCELLENT! Your system is in great shape!');
} else if (percentage >= 75) {
  console.log('👍 GOOD! Minor issues to address.');
} else if (percentage >= 50) {
  console.log('⚠️  NEEDS ATTENTION! Several issues found.');
} else {
  console.log('❌ CRITICAL! Major issues need immediate attention.');
}

console.log('='.repeat(50));
