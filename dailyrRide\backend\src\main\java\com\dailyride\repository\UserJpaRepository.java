package com.dailyride.repository;

import com.dailyride.model.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * JPA Repository for User entity operations with SQLite database.
 */
@Repository
public interface UserJpaRepository extends JpaRepository<User, String> {

    /**
     * Find user by email address.
     */
    Optional<User> findByEmail(String email);

    /**
     * Find user by phone number.
     */
    Optional<User> findByPhone(String phone);

    /**
     * Find all active users.
     */
    List<User> findByIsVerifiedTrue();

    /**
     * Find users by user type.
     */
    List<User> findByUserType(User.UserType userType);

    /**
     * Find drivers (users who can drive).
     */
    @Query("SELECT u FROM User u WHERE u.userType = 'DRIVER' AND u.isVerified = true")
    List<User> findDrivers();

    /**
     * Find passengers (users who can book trips).
     */
    @Query("SELECT u FROM User u WHERE u.userType = 'PASSENGER' AND u.isVerified = true")
    List<User> findPassengers();

    /**
     * Find top-rated drivers.
     */
    @Query("SELECT u FROM User u WHERE u.userType = 'DRIVER' AND u.isVerified = true AND u.totalRatings > 0 ORDER BY u.averageRating DESC, u.totalRatings DESC")
    List<User> findTopRatedDrivers();

    /**
     * Find users with high ratings (above a certain threshold).
     */
    @Query("SELECT u FROM User u WHERE u.isVerified = true AND u.averageRating >= :minRating AND u.totalRatings >= :minRatingCount")
    List<User> findHighlyRatedUsers(@Param("minRating") Double minRating, @Param("minRatingCount") Integer minRatingCount);

    /**
     * Count active users by user type.
     */
    @Query("SELECT COUNT(u) FROM User u WHERE u.isVerified = true AND u.userType = :userType")
    Long countActiveUsersByType(@Param("userType") User.UserType userType);

    /**
     * Check if email exists.
     */
    boolean existsByEmail(String email);

    /**
     * Check if phone exists.
     */
    boolean existsByPhone(String phone);

    /**
     * Find users by gender.
     */
    List<User> findByGender(String gender);

    /**
     * Find admin users.
     */
    @Query("SELECT u FROM User u WHERE u.userType = 'ADMIN'")
    List<User> findAdmins();

    /**
     * Count users by type.
     */
    Long countByUserType(User.UserType userType);
}
