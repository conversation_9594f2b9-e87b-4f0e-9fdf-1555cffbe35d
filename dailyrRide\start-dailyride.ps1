# 🚗 DailyRide - PowerShell Startup Script
# Optimized for Expo SDK 53

Write-Host "🚗 Starting DailyRide Application..." -ForegroundColor Green
Write-Host "=" * 50 -ForegroundColor Blue

# Check if we're in the right directory
if (-not (Test-Path "mobile\package.json")) {
    Write-Host "❌ Error: Please run this script from the dailyrRide root directory" -ForegroundColor Red
    exit 1
}

Write-Host "📱 Starting Frontend (React Native + Expo SDK 53)..." -ForegroundColor Cyan

# Start Frontend in new window
Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd '$PWD\mobile'; Write-Host '📱 DailyRide Frontend Starting...' -ForegroundColor Green; npm start"

Write-Host "⏳ Waiting 5 seconds before starting backend..." -ForegroundColor Yellow
Start-Sleep -Seconds 5

Write-Host "🖥️ Starting Backend (Spring Boot)..." -ForegroundColor Cyan

# Start Backend in new window
Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd '$PWD\backend'; Write-Host '🖥️ DailyRide Backend Starting...' -ForegroundColor Green; mvn spring-boot:run"

Write-Host ""
Write-Host "✅ Both services are starting in separate windows..." -ForegroundColor Green
Write-Host ""
Write-Host "🌐 Access Points:" -ForegroundColor Yellow
Write-Host "📱 Frontend: http://localhost:19006" -ForegroundColor White
Write-Host "🖥️ Backend:  http://localhost:8090/api" -ForegroundColor White
Write-Host "📚 API Docs: http://localhost:8090/api/swagger-ui.html" -ForegroundColor White
Write-Host ""
Write-Host "🔐 Demo Credentials:" -ForegroundColor Yellow
Write-Host "👨‍💼 Admin:     <EMAIL> / admin" -ForegroundColor White
Write-Host "🚗 Driver:    <EMAIL> / driver123" -ForegroundColor White
Write-Host "👤 Passenger: <EMAIL> / passenger123" -ForegroundColor White
Write-Host ""
Write-Host "🎯 Your DailyRide app is starting with Expo SDK 53!" -ForegroundColor Green
Write-Host "⏰ Please wait 2-3 minutes for full initialization..." -ForegroundColor Yellow

# Open browser to frontend after delay
Write-Host "🌐 Opening browser in 30 seconds..." -ForegroundColor Cyan
Start-Sleep -Seconds 30
Start-Process "http://localhost:19006"

Write-Host ""
Write-Host "🎉 DailyRide is now running! Enjoy your ride-sharing app!" -ForegroundColor Green
Write-Host "Press any key to exit this window..."
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
